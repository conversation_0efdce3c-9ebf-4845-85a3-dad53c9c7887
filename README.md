# AI 推理平台

基于FastAPI、PostgreSQL、Redis 的插件化AI推理平台。

## 技术方案

[技术方案.md](docs/%E6%8A%80%E6%9C%AF%E6%96%B9%E6%A1%88.md)

[api设计文档](docs/api)

## 安装

1. 克隆项目
```bash
git clone https://gitee.com/inskylab/ai-platform.git
cd ai-platform
```

2. 创建虚拟环境
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
.\venv\Scripts\activate  # Windows
```

3. 安装依赖
```bash
pip install -r requirements.txt
```

4. 设置环境变量(或修改.env文件)
```bash
cp .env.example .env
```

## 运行

1. 启动开发服务器
```bash
python main.py
```

2. 访问API文档
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## API文档

启动服务器后，可以访问以下地址查看API文档：

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 贡献

欢迎提交Pull Request或创建Issue。
