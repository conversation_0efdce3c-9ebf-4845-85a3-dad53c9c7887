"""
数据库驱动任务调度器
完全基于数据库状态管理，Redis仅用于消息队列
"""
import asyncio
import copy
import os
import time
import uuid
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Optional, Dict, List, Any, cast
from enum import Enum

from sqlalchemy.ext.asyncio import AsyncSession

from app.core.task.redis_queue import redis_queue
from app.services.task_service import task_service
from app.config.settings import settings
from app.services.file_service import file_service
from app.services.plugin_service import plugin_service
from app.services.result_cache_service import result_cache_service
from app.db.session import get_async_db
from app.log import task_logger as logger
from app.models.task_item import TaskItem
from app.plugins.executor import PluginExecutor
from app.utils.file import to_absolute_path
from app.utils.trace_context_utils import traced_async_task, add_span_attributes, add_span_event, create_task_with_context, deserialize_trace_context, run_with_trace_context

from app.utils.worker import worker_manager


class TaskState(Enum):
    """任务状态枚举"""
    INITIALIZING = "initializing"  # 任务项正在创建中
    PENDING = "pending"
    SCHEDULED = "scheduled"  # 已调度但未开始执行
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELED = "canceled"
    TIMEOUT = "timeout"
    RETRY = "retry"


class WorkerState(Enum):
    """Worker状态枚举"""
    IDLE = "idle"
    BUSY = "busy"
    DEAD = "dead"


class TaskScheduler:
    """数据库驱动任务调度器"""

    def __init__(self):
        self.running = False
        self.worker_id: Optional[str] = None
        self.current_running_tasks = 0
        self._lock = asyncio.Lock()
        self._running_tasks: Dict[int, asyncio.Task] = {}

        # 使用Redis作为消息队列
        self.redis_queue = redis_queue

        # 从配置文件加载调度配置
        self.max_concurrent_tasks = settings.TASK_SCHEDULER_MAX_CONCURRENT_TASKS
        self.heartbeat_interval = settings.TASK_SCHEDULER_HEARTBEAT_INTERVAL
        self.task_timeout = settings.TASK_SCHEDULER_TASK_TIMEOUT
        self.lock_timeout = settings.TASK_SCHEDULER_LOCK_TIMEOUT
        self.retry_max_attempts = settings.TASK_SCHEDULER_RETRY_MAX_ATTEMPTS
        self.retry_delay_base = settings.TASK_SCHEDULER_RETRY_DELAY_BASE
        self.batch_size = settings.TASK_SCHEDULER_BATCH_SIZE
        self.poll_interval = settings.TASK_SCHEDULER_POLL_INTERVAL

        # 监控任务
        self._monitor_task = None
        self._heartbeat_task = None
        self._cleanup_task = None

        # 性能指标
        self._metrics = {
            "tasks_processed": 0,
            "tasks_failed": 0,
            "items_processed": 0,
            "items_failed": 0,
            "total_processing_time": 0.0,
            "last_activity": time.time()
        }

    async def start(self):
        """启动调度器"""
        if self.running:
            return

        try:
            # 记录启动时间
            self._start_time = time.time()

            # 获取统一的Worker ID
            self.worker_id = await worker_manager.get_worker_name()

            # 从数据库中恢复pending任务
            await self._load_pending_tasks_from_db()

            self.running = True

            # 启动核心任务
            asyncio.create_task(self._schedule_loop())

            # 启动心跳监控任务
            asyncio.create_task(self._heartbeat_monitor())

            # 启动资源清理任务
            asyncio.create_task(self._cleanup_monitor())

            logger.info(f"数据库驱动任务调度器已启动 - Worker ID: {self.worker_id}")

        except Exception as e:
            logger.error(f"调度器启动失败: {str(e)}")
            raise

    async def stop(self):
        """停止调度器"""
        self.running = False

        # 等待正在执行的任务完成
        if self._running_tasks:
            logger.info(f"等待 {len(self._running_tasks)} 个任务完成...")
            await asyncio.gather(*self._running_tasks.values(), return_exceptions=True)

        logger.info("数据库驱动任务调度器已停止")

    async def get_health_status(self) -> Dict[str, Any]:
        """获取调度器健康状态"""
        try:
            # 检查Redis连接
            redis_healthy = False
            try:
                await self.redis_queue.redis
                redis_healthy = True
            except Exception:
                pass

            # 检查数据库连接
            db_healthy = False
            try:
                async with get_async_db() as db:
                    await db.execute("SELECT 1")
                    db_healthy = True
            except Exception:
                pass

            # 获取任务统计
            running_tasks = len(self._running_tasks)
            global_running = await self.redis_queue.get_global_running_tasks() if redis_healthy else -1
            pending_tasks = await self.redis_queue.get_pending_task_count() if redis_healthy else -1

            return {
                "worker_id": self.worker_id,
                "running": self.running,
                "healthy": redis_healthy and db_healthy,
                "redis_healthy": redis_healthy,
                "db_healthy": db_healthy,
                "local_running_tasks": running_tasks,
                "max_concurrent_tasks": self.max_concurrent_tasks,
                "global_running_tasks": global_running,
                "pending_tasks": pending_tasks,
                "uptime_seconds": time.time() - getattr(self, '_start_time', time.time()),
                "metrics": self._metrics.copy(),
                "config": {
                    "heartbeat_interval": self.heartbeat_interval,
                    "task_timeout": self.task_timeout,
                    "batch_size": self.batch_size,
                    "poll_interval": self.poll_interval
                }
            }
        except Exception as e:
            logger.error(f"Worker {self.worker_id}: 获取健康状态失败: {e}")
            return {
                "worker_id": self.worker_id,
                "healthy": False,
                "error": str(e)
            }

    async def add_task(self, task_id: int, priority: int):
        """添加任务到调度队列"""
        await self.redis_queue.push(task_id, priority)
        logger.info(f"任务 {task_id} (优先级: {priority}) 已加入调度队列")

    async def remove_task(self, task_id: int):
        """从调度队列中移除任务"""
        await self.redis_queue.remove(task_id)
        logger.info(f"任务 {task_id} 已从调度队列中移除")

    async def _load_pending_tasks_from_db(self):
        """从数据库加载待处理的任务到Redis队列"""
        # 使用分布式锁确保只有一个worker执行初始化
        lock_key = "ai_platform:task_queue:init_lock"
        lock_acquired = await self.redis_queue.acquire_lock(lock_key, timeout=30)

        if not lock_acquired:
            logger.info(f"Worker {self.worker_id}: 其他worker正在执行任务初始化，跳过")
            return

        try:
            logger.info(f"Worker {self.worker_id}: 开始加载待处理任务到Redis队列")
            async with get_async_db() as db:
                from app.repositories.task_repository import task_repository
                pending_tasks = await task_repository.get_by_status(db, status="pending")

                count = 0
                for task in pending_tasks:
                    await self.redis_queue.push(task.id, task.priority)
                    count += 1

                if count > 0:
                    logger.info(f"Worker {self.worker_id}: 从数据库加载了 {count} 个待处理任务到Redis队列")
                else:
                    logger.info(f"Worker {self.worker_id}: 没有待处理的任务需要加载")
        except Exception as e:
            logger.error(f"Worker {self.worker_id}: 加载待处理任务失败: {str(e)}")
        finally:
            # 释放锁
            await self.redis_queue.release_lock(lock_key)

    async def _schedule_loop(self):
        """主调度循环"""
        while self.running:
            try:
                # 检查本地并发限制
                if self.current_running_tasks >= self.max_concurrent_tasks:
                    await asyncio.sleep(self.poll_interval)
                    continue

                # 检查全局并发限制
                global_running = await self.redis_queue.get_global_running_tasks()
                if global_running >= self.redis_queue.GLOBAL_MAX_CONCURRENT_TASKS:
                    logger.debug(f"Worker {self.worker_id}: 全局并发达到上限 ({global_running}), 等待")
                    # 全局并发达到上限时，等待更长时间
                    await asyncio.sleep(self.poll_interval * 2)
                    continue

                task_result = await self.redis_queue.pop()
                if not task_result:
                    # 没有任务时，使用动态等待时间
                    await asyncio.sleep(self.poll_interval)
                    continue

                task_id, trace_context_str = task_result

                # 恢复trace context
                if trace_context_str:
                    trace_context = deserialize_trace_context(trace_context_str)
                    logger.debug(f"Worker {self.worker_id}: 恢复任务 {task_id} 的trace context")
                else:
                    trace_context = None
                    logger.debug(f"Worker {self.worker_id}: 任务 {task_id} 没有trace context")

                lock_acquired = await self.redis_queue.acquire_lock(
                    f"task:{task_id}", self.lock_timeout
                )

                if not lock_acquired:
                    logger.debug(f"Worker {self.worker_id}: 任务 {task_id} 锁获取失败，重新入队")
                    # 重新将任务加入队列，避免任务丢失
                    async with get_async_db() as db:
                        task = await task_manager.get_task_model(db, task_id)
                        if task and task.status in [TaskState.PENDING.value, TaskState.RETRY.value]:
                            await self.redis_queue.push(task_id, task.priority, trace_context_str)
                    continue

                async with get_async_db() as db:
                    task = await task_manager.get_task_model(db, task_id)
                    if not task:
                        await self.redis_queue.release_lock(f"task:{task_id}")
                        continue

                    # 只处理 pending 和 retry 状态的任务，跳过 initializing 状态
                    if task.status not in [TaskState.PENDING.value, TaskState.RETRY.value]:
                        if task.status == TaskState.INITIALIZING.value:
                            logger.debug(f"Worker {self.worker_id}: 任务 {task_id} 仍在初始化中，重新入队")
                            await self.redis_queue.push(task_id, task.priority, trace_context_str)
                        await self.redis_queue.release_lock(f"task:{task_id}")
                        continue

                await self._start_task_execution(task_id, trace_context)

            except Exception as e:
                logger.error(f"Worker {self.worker_id}: 调度循环发生错误: {str(e)}")
                # 根据错误类型决定等待时间
                if isinstance(e, (ConnectionError, TimeoutError)):
                    # 连接错误，等待更长时间
                    await asyncio.sleep(self.poll_interval * 5)
                else:
                    await asyncio.sleep(self.poll_interval)

    async def _start_task_execution(self, task_id: int, trace_context=None):
        """启动任务执行"""
        async with self._task_execution_context():
            # 增加全局运行任务计数
            await self.redis_queue.increment_global_running_tasks()

            task_coroutine = self._execute_task_with_monitoring(task_id)

            # 如果有trace context，在该context中执行任务
            if trace_context:
                task_coroutine = run_with_trace_context(task_coroutine, trace_context)
                logger.debug(f"Worker {self.worker_id}: 在恢复的trace context中执行任务 {task_id}")

            execution_task = asyncio.create_task(
                task_coroutine,
                name=f"execute_task_{task_id}"
            )
            self._running_tasks[task_id] = execution_task
            execution_task.add_done_callback(
                lambda t: create_task_with_context(
                    self._on_task_completed(task_id, t),
                    name=f"task_completed_{task_id}"
                )
            )

    async def _on_task_completed(self, task_id: int, future: asyncio.Task):
        """任务完成时的回调"""
        try:
            # 减少全局运行任务计数
            await self.redis_queue.decrement_global_running_tasks()

            # 释放任务锁
            await self.redis_queue.release_lock(f"task:{task_id}")

            if task_id in self._running_tasks:
                del self._running_tasks[task_id]

            exc = future.exception()
            if exc:
                logger.error(f"Worker {self.worker_id}: 任务 {task_id} 执行异常: {exc}")
            else:
                logger.info(f"Worker {self.worker_id}: 任务 {task_id} 执行完成")
        except Exception as e:
            logger.error(f"Worker {self.worker_id}: 任务 {task_id} 完成回调异常: {e}")

    async def _heartbeat_monitor(self):
        """定期心跳监控"""
        while self.running:
            try:
                # 为所有正在运行的任务更新心跳
                for task_id in list(self._running_tasks.keys()):
                    await self.redis_queue.update_heartbeat(task_id)

                # 等待心跳间隔
                await asyncio.sleep(self.heartbeat_interval)
            except Exception as e:
                logger.error(f"Worker {self.worker_id}: 心跳监控异常: {e}")
                await asyncio.sleep(self.heartbeat_interval)

    async def _cleanup_monitor(self):
        """资源清理监控"""
        cleanup_interval = self.heartbeat_interval * 3  # 清理间隔为心跳间隔的3倍

        while self.running:
            try:
                # 检查卡住的任务
                stale_tasks = await self.redis_queue.get_stale_tasks(
                    timeout_seconds=settings.TASK_SCHEDULER_STALE_TASK_TIMEOUT
                )

                if stale_tasks:
                    logger.warning(f"Worker {self.worker_id}: 发现 {len(stale_tasks)} 个可能卡住的任务: {stale_tasks}")

                    # 清理本地已完成但未正确清理的任务
                    for task_id in list(self._running_tasks.keys()):
                        task = self._running_tasks[task_id]
                        if task.done():
                            logger.info(f"Worker {self.worker_id}: 清理已完成的任务 {task_id}")
                            del self._running_tasks[task_id]
                            await self.redis_queue.decrement_global_running_tasks()

                # 检查内存使用情况
                running_tasks_count = len(self._running_tasks)
                if running_tasks_count > self.max_concurrent_tasks:
                    logger.warning(f"Worker {self.worker_id}: 运行任务数 ({running_tasks_count}) 超过限制 ({self.max_concurrent_tasks})")

                await asyncio.sleep(cleanup_interval)

            except Exception as e:
                logger.error(f"Worker {self.worker_id}: 资源清理监控异常: {e}")
                await asyncio.sleep(cleanup_interval)

    @traced_async_task(
        "task_execution_{task_id}",
        attributes={
            "component": "scheduler",
            "operation": "execute_task_with_monitoring"
        }
    )
    async def _execute_task_with_monitoring(self, task_id: int):
        """带监控的任务执行"""
        start_time = time.time()

        # 添加span属性
        add_span_attributes(
            worker_id=self.worker_id,
            task_id=task_id
        )
        add_span_event("task_execution_started")

        try:
            async with get_async_db() as db:
                add_span_attributes(
                    db_operation="task_status_update",
                    task_id=task_id,
                    new_status=TaskState.RUNNING.value,
                    worker_id=self.worker_id
                )
                await task_service.update_task_status(
                    db, task_id=task_id, status=TaskState.RUNNING.value
                )

            await self.redis_queue.update_heartbeat(task_id)

            await self._execute_task_logic(task_id)

            # 在任务处理完所有项后，再次检查所有项的状态
            # 确保任务状态是最新的
            is_completed = await self._check_task_completion(task_id)
            if is_completed:
                # 计算处理时间
                processing_time = time.time() - start_time

                async with get_async_db() as db:
                    add_span_attributes(
                        db_operation="task_completion_update",
                        task_id=task_id,
                        new_status=TaskState.COMPLETED.value,
                        worker_id=self.worker_id,
                        processing_time=processing_time
                    )
                    await task_service.update_task_status(
                        db, task_id=task_id, status=TaskState.COMPLETED.value
                    )

                logger.info(f"任务 {task_id} 执行成功，耗时: {processing_time:.2f}s")

                # 更新性能指标
                self._metrics["tasks_processed"] += 1
                self._metrics["total_processing_time"] += processing_time
                self._metrics["last_activity"] = time.time()

        except asyncio.CancelledError:
            logger.warning(f"任务 {task_id} 被取消")
            async with get_async_db() as db:
                await task_service.update_task_status(
                    db, task_id=task_id, status=TaskState.CANCELED.value
                )
            raise

        except Exception as e:
            logger.error(f"任务 {task_id} 执行失败: {str(e)}")

            # 更新失败指标
            self._metrics["tasks_failed"] += 1
            self._metrics["last_activity"] = time.time()

            await self._handle_task_failure(task_id, str(e))

        finally:
            await self.redis_queue.release_lock(f"task:{task_id}")

    async def _execute_task_logic(self, task_id: int):
        """执行任务的具体逻辑（数据库驱动版本）"""
        # 使用单个数据库连接来减少连接开销
        async with get_async_db() as db:
            while self.running:
                try:
                    pending_items = await task_manager.get_pending_items(
                        db, task_id=task_id, limit=self.batch_size
                    )

                    if not pending_items:
                        # 检查任务是否已完成
                        is_completed = await self._check_task_completion(task_id)
                        logger.debug(f"Worker {self.worker_id}: 任务 {task_id} 检查完成状态: {is_completed}")

                        if is_completed:
                            logger.info(f"Worker {self.worker_id}: 任务 {task_id} 所有子任务已处理，准备结束")
                            break
                        else:
                            # 没有待处理项但任务未完成，可能存在数据不一致
                            # 等待一段时间后重新检查，给任务项创建更多时间
                            logger.debug(f"Worker {self.worker_id}: 任务 {task_id} 暂无待处理项，等待中...")
                            await asyncio.sleep(2)  # 增加等待时间
                            continue

                    # 批量处理任务项
                    for item in pending_items:
                        if not self.running:
                            raise asyncio.CancelledError("调度器已停止")

                        await self.redis_queue.update_heartbeat(task_id)

                        try:
                            await self._process_task_item(db, item)
                        except Exception as e:
                            logger.error(f"Worker {self.worker_id}: 处理任务项 {item.id} 失败: {str(e)}")
                            try:
                                await self._update_task_item_status(db, item.id, "failed", str(e))
                            except Exception as update_error:
                                logger.error(f"Worker {self.worker_id}: 更新任务项 {item.id} 状态失败: {str(update_error)}")
                                # 即使状态更新失败，也要继续处理其他任务项

                except Exception as e:
                    logger.error(f"Worker {self.worker_id}: 任务 {task_id} 执行循环异常: {str(e)}")
                    await asyncio.sleep(self.poll_interval)
                    # 重新获取数据库连接
                    break

    async def _check_task_completion(self, task_id: int) -> bool:
        """检查任务是否已完成（基于数据库查询）"""
        async with get_async_db() as db:
            # 获取任务总项数
            task = await task_service.get_task_model(db, task_id)
            if not task:
                logger.error(f"任务 {task_id} 不存在")
                return False

            # 如果任务仍在初始化中，肯定没有完成
            if task.status == TaskState.INITIALIZING.value:
                logger.debug(f"任务 {task_id} 仍在初始化中，未完成")
                return False

            total_items = task.total_items

            # 获取已完成的任务项数量（包括completed、failed和canceled状态）
            from app.repositories.task_item_repository import task_item_repository
            completed_items_count = await task_item_repository.count_by_task_id(
                db,
                task_id=task_id,
                status=["completed", "failed", "canceled"]
            )

            # 获取待处理的任务项数量
            pending_items_count = await task_item_repository.count_by_task_id(
                db,
                task_id=task_id,
                status=["pending", "running"]
            )

            # 检查数据一致性：已完成 + 待处理 应该等于总数
            total_accounted = completed_items_count + pending_items_count
            if total_accounted != total_items:
                logger.warning(f"任务 {task_id} 数据不一致: 总数={total_items}, 已完成={completed_items_count}, 待处理={pending_items_count}")
                # 如果数据不一致，认为任务未完成，需要进一步处理
                return False

            # 如果所有任务项都已完成，则返回True
            is_completed = total_items > 0 and completed_items_count >= total_items
            if is_completed:
                logger.info(f"任务 {task_id} 完成检查: 总数={total_items}, 已完成={completed_items_count}")

            return is_completed

    async def _handle_task_failure(self, task_id: int, error_msg: str):
        """处理任务失败"""
        try:
            async with get_async_db() as db:
                await task_service.update_task_status(
                    db, task_id=task_id, status=TaskState.FAILED.value, error=error_msg
                )
        except Exception as e:
            logger.error(f"标记任务 {task_id} 为失败时出错: {str(e)}")

    @asynccontextmanager
    async def _task_execution_context(self):
        """任务执行上下文管理器"""
        async with self._lock:
            self.current_running_tasks += 1
        try:
            yield
        finally:
            async with self._lock:
                self.current_running_tasks -= 1

    @traced_async_task(
        "task_item_processing_{item_id}",
        attributes={
            "component": "scheduler",
            "operation": "process_task_item"
        }
    )
    async def _process_task_item(self, db: AsyncSession, item: TaskItem):
        """处理任务项"""
        # 添加span属性
        add_span_attributes(
            worker_id=self.worker_id,
            task_id=item.task_id,
            item_id=item.id,
            plugin_code=item.plugin_code,
            plugin_version=item.plugin_version,
            file_id=item.file_id
        )
        add_span_event("task_item_processing_started")

        try:
            # 1. 获取文件和插件信息（缓存检查需要这些信息）
            file = await file_service.get_file(file_id=item.file_id)
            if not file:
                raise ValueError(f"文件不存在: {item.file_id}")
            plugin_info = await plugin_service.get_plugin_info(
                db,
                plugin_code=item.plugin_code,
                plugin_version=item.plugin_version,
                absolute_path=True
            )

            # 2. 缓存快速路径检查
            add_span_event("cache_check_started")
            cached_result = await result_cache_service.get_cache(
                db,
                file_hash=file.file_hash,
                plugin_code=item.plugin_code,
                plugin_version=item.plugin_version,
                params=item.params
            )
            add_span_event("cache_check_completed")

            if cached_result:
                # 缓存命中：直接从pending跳到completed，跳过running状态
                add_span_event("cache_hit_fast_path")
                logger.info(f"缓存命中，跳过插件执行 - 任务项: {item.id}")

                # 标记为来自缓存并直接完成
                await self._update_task_item_status(
                    db,
                    item.id,
                    "completed",
                    result=cached_result["result"],
                    process_time=cached_result["process_time"]
                )

                # 更新from_cache标志
                await task_manager.update_task_item_direct(
                    db,
                    id=item.id,
                    update_data={"from_cache": True}
                )

                add_span_event("task_item_processing_completed_from_cache")
                self._metrics["items_processed"] += 1
                return  # 直接返回，跳过后续的插件执行流程

            # 缓存未命中：继续正常流程，设置为running状态
            add_span_event("cache_miss_normal_path")
            await self._update_task_item_status(db, item.id, "running")
            # 4. 构建输入数据
            input_data = {
                "file_path": to_absolute_path(file.local_path),
                "file_url": item.file_url,
                "task_id": item.task_id,
                "task_item_id": item.id,
                "file_type": file.file_type,
                "media_type": file.media_type,
                "params": item.params
            }

            # 深拷贝输入参数，避免原始参数被修改
            safe_input_data = copy.deepcopy(input_data)
            safe_plugin_info = copy.deepcopy(plugin_info)

            # 5. 直接调用PluginExecutor执行插件
            add_span_event("plugin_execution_started")
            result = await PluginExecutor.execute_plugin(
                plugin_info=safe_plugin_info,
                input_data=safe_input_data
            )
            add_span_event("plugin_execution_completed")

            plugin_result = result.get("result")
            progress_time = result.get("progress_time")

            # 6. 保存结果到缓存
            add_span_event("cache_saving_started")
            await result_cache_service.set_cache(
                db,
                file_hash=file.file_hash,
                plugin_code=item.plugin_code,
                plugin_version=item.plugin_version,
                result=plugin_result,
                process_time=progress_time,
                params=item.params
            )
            add_span_event("cache_saving_completed")

            await self._update_task_item_status(
                db,
                item.id,
                "completed",
                result=plugin_result,
                process_time=progress_time
            )
            add_span_event("task_item_processing_completed")

            # 更新任务项处理指标
            self._metrics["items_processed"] += 1
        except Exception as e:
            add_span_event("task_item_processing_failed", {"error": str(e)})
            logger.error(f"处理任务项 {item.id} 失败: {str(e)}")
            await self._update_task_item_status(db, item.id, "failed", str(e))

            # 更新任务项失败指标
            self._metrics["items_failed"] += 1

            raise

    async def _update_task_item_status(
            self,
            db: AsyncSession,
            id: int,
            status: str,
            error: Optional[str] = None,
            result: Optional[Dict[str, Any]] = None,
            process_time: Optional[Dict[str, Any]] = None
    ):
        """更新任务项状态"""
        update_data = {
            "status": status,
            "updated_at": datetime.now()
        }
        if error is not None:
            update_data["error"] = error
        if result is not None:
            update_data["result"] = result
        if process_time is not None:
            update_data["process_time"] = process_time
        if status == "running":
            update_data["started_at"] = datetime.now()
        elif status in ["completed", "failed", "canceled"]:
            update_data["completed_at"] = datetime.now()

        add_span_attributes(
            db_operation="task_item_status_update",
            item_id=id,
            new_status=status,
            worker_id=self.worker_id,
            has_error=error is not None,
            has_result=result is not None
        )
        # 使用直接更新方法，避免先查询再更新的性能开销
        await task_service.update_task_item_direct(db, id=id, update_data=update_data)


# 创建全局数据库调度器实例
scheduler = TaskScheduler()

