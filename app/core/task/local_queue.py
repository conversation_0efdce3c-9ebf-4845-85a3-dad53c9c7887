import asyncio
import heapq
from typing import Optional


class PriorityQueue:
    """优先级队列实现"""

    def __init__(self):
        self._queue = []
        self._task_set = set()  # 用于快速查找任务是否在队列中
        self._lock = asyncio.Lock()

    async def push(self, task_id: int, priority: int):
        """
        将任务加入优先级队列
        优先级越大，优先级越高
        """
        async with self._lock:
            if task_id not in self._task_set:
                # 使用负优先级是因为heapq是最小堆
                heapq.heappush(self._queue, (-priority, task_id))
                self._task_set.add(task_id)

    async def pop(self) -> Optional[int]:
        """获取优先级最高的任务"""
        async with self._lock:
            if not self._queue:
                return None
            _, task_id = heapq.heappop(self._queue)
            self._task_set.remove(task_id)
            return task_id

    async def remove(self, task_id: int):
        """从队列中移除指定任务"""
        async with self._lock:
            if task_id in self._task_set:
                self._queue = [(p, tid) for p, tid in self._queue if tid != task_id]
                heapq.heapify(self._queue)
                self._task_set.remove(task_id)

    async def is_empty(self) -> bool:
        """检查队列是否为空"""
        async with self._lock:
            return len(self._queue) == 0
