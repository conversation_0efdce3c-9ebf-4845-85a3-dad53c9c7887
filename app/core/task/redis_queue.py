"""
基于Redis的优先级队列实现
用于多workers环境下的任务队列共享
"""
import asyncio
from typing import Optional, List, Set, Tuple
import time

from redis import asyncio as redis_asyncio
from redis.asyncio import Redis

from app.config.settings import settings
from app.log import task_logger as logger


class RedisQueue:
    """基于Redis的优先级队列实现"""

    # Redis键名前缀
    KEY_PREFIX = "ai_platform:task_queue:"
    # 队列名
    QUEUE_KEY = f"{KEY_PREFIX}priority_queue"
    # 任务集合（用于快速查找任务是否在队列中）
    TASK_SET_KEY = f"{KEY_PREFIX}task_set"
    # trace context存储（哈希表：task_id -> trace_context）
    TASK_CONTEXT_KEY = f"{KEY_PREFIX}task_contexts"
    # 分布式锁前缀
    LOCK_PREFIX = f"{KEY_PREFIX}lock:"
    # 锁超时时间（秒）
    LOCK_TIMEOUT = 30
    # 心跳key前缀
    HEARTBEAT_PREFIX = f"{KEY_PREFIX}heartbeat:"
    # 心跳超时时间（秒）
    HEARTBEAT_TIMEOUT = 60
    # 子任务计数器键前缀
    ITEM_COUNT_PREFIX = f"{KEY_PREFIX}item_counts:"
    # 全局运行任务计数器
    GLOBAL_RUNNING_TASKS_KEY = f"{KEY_PREFIX}global_running_tasks"

    def __init__(self):
        """初始化Redis队列"""
        self._redis = None
        self._lock = asyncio.Lock()
        self._initialized = False

        # 从配置加载参数
        from app.config.settings import settings
        self.GLOBAL_MAX_CONCURRENT_TASKS = settings.TASK_SCHEDULER_GLOBAL_MAX_CONCURRENT_TASKS

    async def initialize(self):
        """初始化Redis连接"""
        if self._initialized:
            return

        async with self._lock:
            if self._initialized:
                return
                
            # 获取Redis配置
            redis_url = settings.REDIS_URL
            if not redis_url:
                raise ValueError("未配置Redis URL，请在环境变量或配置文件中设置REDIS_URL")
            
            # 将RedisDsn对象转换为字符串
            redis_url_str = str(redis_url)
                
            # 创建Redis连接
            self._redis = await redis_asyncio.from_url(
                redis_url_str,
                encoding="utf-8",
                decode_responses=True
            )
            
            # 测试连接
            try:
                await self._redis.ping()
                logger.info(f"Redis连接成功: {redis_url}")
                self._initialized = True
            except Exception as e:
                logger.error(f"Redis连接失败: {str(e)}")
                raise

    @property
    async def redis(self) -> Redis:
        """获取Redis连接"""
        if not self._initialized:
            await self.initialize()
        return self._redis

    async def push(self, task_id: int, priority: int, trace_context: str = None):
        """
        将任务加入优先级队列（原子操作，避免重复添加）

        Args:
            task_id: 任务ID
            priority: 优先级（越大优先级越高）
            trace_context: 序列化的trace context字符串
        """
        redis_client = await self.redis

        # 使用 Lua 脚本确保检查和添加的原子性
        lua_script = """
        local task_id = ARGV[1]
        local priority = ARGV[2]
        local trace_context = ARGV[3]
        local queue_key = KEYS[1]
        local set_key = KEYS[2]
        local context_key = KEYS[3]

        -- 检查任务是否已在集合中
        if redis.call('SISMEMBER', set_key, task_id) == 0 then
            -- 任务不存在，添加到队列和集合
            redis.call('ZADD', queue_key, priority, task_id)
            redis.call('SADD', set_key, task_id)
            -- 如果有trace context，存储到哈希表中
            if trace_context and trace_context ~= "" then
                redis.call('HSET', context_key, task_id, trace_context)
            end
            return 1
        else
            -- 任务已存在
            return 0
        end
        """

        result = await redis_client.eval(
            lua_script,
            3,  # 键的数量
            self.QUEUE_KEY,
            self.TASK_SET_KEY,
            self.TASK_CONTEXT_KEY,
            str(task_id),
            str(priority),
            trace_context or ""
        )

        if result == 1:
            logger.info(f"任务 {task_id} (优先级: {priority}) 已加入Redis队列")
        else:
            logger.debug(f"任务 {task_id} 已在队列中，跳过添加")

    async def pop(self) -> Optional[Tuple[int, str]]:
        """
        获取优先级最高的任务

        Returns:
            (任务ID, trace_context)的元组或None（队列为空时）
        """
        redis_client = await self.redis
        # 使用ZPOPMAX获取优先级最高的任务
        result = await redis_client.zpopmax(self.QUEUE_KEY)
        if not result:
            return None

        task_id_str, _ = result[0]  # 结果格式为 [(task_id, score)]

        # 获取trace context
        trace_context = await redis_client.hget(self.TASK_CONTEXT_KEY, task_id_str)

        # 从集合和哈希表中移除任务ID
        await redis_client.srem(self.TASK_SET_KEY, task_id_str)
        await redis_client.hdel(self.TASK_CONTEXT_KEY, task_id_str)

        return (int(task_id_str), trace_context or "")

    async def remove(self, task_id: int):
        """
        从队列中移除指定任务

        Args:
            task_id: 要移除的任务ID
        """
        redis_client = await self.redis
        # 从有序集合中移除任务
        await redis_client.zrem(self.QUEUE_KEY, str(task_id))
        # 从集合中移除任务ID
        await redis_client.srem(self.TASK_SET_KEY, str(task_id))
        # 从trace context哈希表中移除
        await redis_client.hdel(self.TASK_CONTEXT_KEY, str(task_id))
        logger.info(f"任务 {task_id} 已从Redis队列中移除")

    async def is_empty(self) -> bool:
        """
        检查队列是否为空
        
        Returns:
            队列为空返回True，否则返回False
        """
        redis_client = await self.redis
        # 获取有序集合的大小
        size = await redis_client.zcard(self.QUEUE_KEY)
        return size == 0

    async def get_all_tasks(self) -> List[Tuple[int, int]]:
        """
        获取队列中所有任务及其优先级
        
        Returns:
            任务ID和优先级的元组列表 [(task_id, priority), ...]
        """
        redis_client = await self.redis
        # 获取有序集合中的所有成员和分数
        result = await redis_client.zrange(
            self.QUEUE_KEY, 
            0, -1, 
            withscores=True,
            desc=True  # 按优先级降序排列
        )
        # 将字符串ID转换为整数
        return [(int(task_id), int(score)) for task_id, score in result]

    async def get_pending_task_count(self) -> int:
        """
        获取待处理任务数量
        
        Returns:
            队列中的任务数量
        """
        redis_client = await self.redis
        return await redis_client.zcard(self.QUEUE_KEY)

    async def acquire_lock(self, resource_name: str, timeout: int = LOCK_TIMEOUT) -> bool:
        """
        获取分布式锁
        
        Args:
            resource_name: 资源名称
            timeout: 锁超时时间（秒）
            
        Returns:
            获取成功返回True，否则返回False
        """
        redis_client = await self.redis
        lock_key = f"{self.LOCK_PREFIX}{resource_name}"
        # 使用SET NX EX实现分布式锁
        expiration = int(time.time()) + timeout
        result = await redis_client.set(lock_key, expiration, nx=True, ex=timeout)
        return bool(result)

    async def release_lock(self, resource_name: str) -> bool:
        """
        释放分布式锁
        
        Args:
            resource_name: 资源名称
            
        Returns:
            释放成功返回True，否则返回False
        """
        redis_client = await self.redis
        lock_key = f"{self.LOCK_PREFIX}{resource_name}"
        # 删除锁键
        result = await redis_client.delete(lock_key)
        return result > 0

    async def update_heartbeat(self, task_id: int):
        """
        更新任务心跳
        
        Args:
            task_id: 任务ID
        """
        redis_client = await self.redis
        heartbeat_key = f"{self.HEARTBEAT_PREFIX}{task_id}"
        # 更新心跳时间戳
        await redis_client.set(heartbeat_key, int(time.time()), ex=self.HEARTBEAT_TIMEOUT)

    async def get_heartbeat(self, task_id: int) -> Optional[int]:
        """
        获取任务最后心跳时间
        
        Args:
            task_id: 任务ID
            
        Returns:
            最后心跳时间戳或None（无心跳记录）
        """
        redis_client = await self.redis
        heartbeat_key = f"{self.HEARTBEAT_PREFIX}{task_id}"
        result = await redis_client.get(heartbeat_key)
        return int(result) if result else None

    async def increment_global_running_tasks(self) -> int:
        """
        增加全局运行任务计数

        Returns:
            当前全局运行任务数
        """
        redis_client = await self.redis
        return await redis_client.incr(self.GLOBAL_RUNNING_TASKS_KEY)

    async def decrement_global_running_tasks(self) -> int:
        """
        减少全局运行任务计数

        Returns:
            当前全局运行任务数
        """
        redis_client = await self.redis
        current = await redis_client.decr(self.GLOBAL_RUNNING_TASKS_KEY)
        # 确保不会小于0
        if current < 0:
            await redis_client.set(self.GLOBAL_RUNNING_TASKS_KEY, 0)
            return 0
        return current

    async def get_global_running_tasks(self) -> int:
        """
        获取全局运行任务数

        Returns:
            当前全局运行任务数
        """
        redis_client = await self.redis
        result = await redis_client.get(self.GLOBAL_RUNNING_TASKS_KEY)
        return int(result) if result else 0

    async def get_stale_tasks(self, timeout_seconds: int = 300) -> List[int]:
        """
        获取可能卡住的任务（心跳超时的任务）

        Args:
            timeout_seconds: 心跳超时时间（秒）

        Returns:
            可能卡住的任务ID列表
        """
        redis_client = await self.redis
        current_time = int(time.time())
        stale_tasks = []

        # 获取所有心跳键
        heartbeat_keys = await redis_client.keys(f"{self.HEARTBEAT_PREFIX}*")

        for key in heartbeat_keys:
            try:
                last_heartbeat = await redis_client.get(key)
                if last_heartbeat:
                    last_heartbeat_time = int(last_heartbeat)
                    if current_time - last_heartbeat_time > timeout_seconds:
                        # 提取任务ID
                        task_id = int(key.replace(self.HEARTBEAT_PREFIX, ""))
                        stale_tasks.append(task_id)
            except (ValueError, TypeError):
                continue

        return stale_tasks

    async def is_task_active(self, task_id: int) -> bool:
        """
        检查任务是否活跃（有最近心跳）
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务活跃返回True，否则返回False
        """
        heartbeat = await self.get_heartbeat(task_id)
        if not heartbeat:
            return False
            
        # 检查心跳是否超时
        now = int(time.time())
        return now - heartbeat < self.HEARTBEAT_TIMEOUT



# 创建全局Redis队列实例
redis_queue = RedisQueue() 