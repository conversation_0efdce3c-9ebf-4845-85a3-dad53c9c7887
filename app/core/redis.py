"""
Redis缓存管理模块
"""
from typing import Any, Dict, Optional, Union
import json

import redis.asyncio as redis
from redis.asyncio import Redis

from app.config.settings import settings


class RedisManager:
    """Redis管理器类"""
    
    def __init__(self) -> None:
        """初始化Redis连接"""
        self._redis_client: Optional[Redis] = None
    
    @property
    async def client(self) -> Redis:
        """获取Redis客户端实例（懒加载）"""
        if self._redis_client is None:
            self._redis_client = await redis.from_url(
                str(settings.REDIS_URL),
                encoding="utf-8",
                decode_responses=True,
                socket_timeout=5.0,
                socket_connect_timeout=5.0
            )
        return self._redis_client
    
    async def get(self, key: str) -> Optional[str]:
        """获取字符串值"""
        client = await self.client
        return await client.get(key)
    
    async def set(
        self, 
        key: str, 
        value: str, 
        expire: Optional[int] = None
    ) -> bool:
        """设置字符串值，可选过期时间（秒）"""
        client = await self.client
        result = await client.set(key, value)
        if expire:
            await client.expire(key, expire)
        return result
    
    async def get_json(self, key: str) -> Optional[Dict[str, Any]]:
        """获取JSON值"""
        client = await self.client
        data = await client.get(key)
        if data:
            return json.loads(data)
        return None
    
    async def set_json(
        self, 
        key: str, 
        value: Dict[str, Any], 
        expire: Optional[int] = None
    ) -> bool:
        """设置JSON值，可选过期时间（秒）"""
        client = await self.client
        result = await client.set(key, json.dumps(value))
        if expire:
            await client.expire(key, expire)
        return result
    
    async def delete(self, key: str) -> int:
        """删除键"""
        client = await self.client
        return await client.delete(key)
    
    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        client = await self.client
        return await client.exists(key) > 0
    
    async def incr(self, key: str, amount: int = 1) -> int:
        """增加计数器值"""
        client = await self.client
        return await client.incrby(key, amount)
    
    async def zadd(self, key: str, score: float, member: str) -> int:
        """添加有序集合成员"""
        client = await self.client
        return await client.zadd(key, {member: score})
    
    async def close(self) -> None:
        """关闭Redis连接"""
        if self._redis_client:
            await self._redis_client.close()


# 创建全局Redis管理器实例
redis_manager = RedisManager() 