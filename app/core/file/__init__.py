"""
文件服务模块
提供文件上传、下载、删除和临时URL生成等功能
"""

"""
文件服务模块已迁移到Service层
请使用 app.services.file_service 替代
"""
from app.core.file.downloader import file_downloader

# 延迟导入避免循环依赖
def get_file_manager():
    from app.services.file_service import file_service
    return file_service

# 为了向后兼容，保留file_manager属性
file_manager = None

def __getattr__(name):
    if name == "file_manager":
        return get_file_manager()
    raise AttributeError(f"module '{__name__}' has no attribute '{name}'")

__all__ = [
    "file_manager",
    "file_downloader",
]