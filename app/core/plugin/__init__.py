"""
插件管理包
"""
"""
插件管理包 - 已迁移到Service层
请使用 app.services.plugin_service 替代
"""
from app.core.plugin.parser import PluginParser, PluginParseError

# 延迟导入避免循环依赖
def get_plugin_manager():
    from app.services.plugin_service import plugin_service
    return plugin_service

# 为了向后兼容，保留plugin_manager属性
plugin_manager = None

def __getattr__(name):
    if name == "plugin_manager":
        return get_plugin_manager()
    raise AttributeError(f"module '{__name__}' has no attribute '{name}'")

__all__ = [
    "plugin_manager",
    "PluginParser",
    "PluginParseError",
]