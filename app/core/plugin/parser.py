"""
插件配置解析模块
解析插件配置文件并提取元数据
"""
import os
import yaml
import importlib.util
from typing import Dict, Any, Tuple, Optional

from app.schemas.plugin import PluginCreate, PluginType, EngineType, FileType, MediaType
from app.config.settings import settings
from app.utils.file import to_absolute_path


class PluginParseError(Exception):
    """插件解析错误"""
    pass


class PluginParser:
    """插件配置解析器"""
    
    @staticmethod
    def parse_config_file(config_file_path: str) -> Dict[str, Any]:
        """
        解析YAML配置文件
        
        Args:
            config_file_path: 配置文件路径
            
        Returns:
            Dict[str, Any]: 解析后的配置字典
        
        Raises:
            PluginParseError: 解析错误
        """
        try:
            with open(config_file_path, 'rb') as f:
                content = f.read().decode('utf-8')
                config = yaml.safe_load(content)
                
            if not isinstance(config, dict):
                raise PluginParseError("配置文件必须是一个YAML字典")
            
            if 'metadata' not in config or not isinstance(config['metadata'], dict):
                raise PluginParseError("配置文件必须包含 metadata 部分")
                
            if 'model' not in config or not isinstance(config['model'], dict):
                raise PluginParseError("配置文件必须包含 model 部分")
            
            # 扁平化处理，将metadata和model层的内容提取到顶层
            flattened_config = {}
            
            # 处理元数据字段
            metadata = config['metadata']
            flattened_config['plugin_code'] = metadata.get('id')
            flattened_config['plugin_version'] = metadata.get('version')
            flattened_config['name'] = metadata.get('name')
            flattened_config['description'] = metadata.get('description')
            flattened_config['author'] = metadata.get('author')
            flattened_config['type'] = metadata.get('type')
            
            # 处理模型字段
            model = config['model']
            flattened_config['engine'] = model.get('engine')
            
            # 处理列表字段，确保类型正确
            def ensure_list(value) -> list:
                if value is None:
                    return []
                if isinstance(value, str):
                    # 如果是字符串，尝试将其作为单个元素的列表
                    return [value]
                if isinstance(value, (list, tuple)):
                    return list(value)
                return []
            
            # 处理input_file_type字段
            input_file_type = model.get('input_file_type')
            flattened_config['input_file_type'] = ensure_list(input_file_type)
            
            # 处理input_media_type字段
            input_media_type = model.get('input_media_type')
            flattened_config['input_media_type'] = ensure_list(input_media_type)
            
            # 处理classes字段
            classes = model.get('classes')
            flattened_config['classes'] = ensure_list(classes)
            
            # 附加处理配置
            if 'processing' in config:
                flattened_config['processing'] = config['processing']
            
            # 附加资源需求
            if 'resources' in config:
                flattened_config['resources'] = config['resources']
            
            return flattened_config
            
        except yaml.YAMLError as e:
            raise PluginParseError(f"YAML解析错误: {str(e)}")
        except UnicodeDecodeError as e:
            raise PluginParseError(f"配置文件编码错误: {str(e)}")
        except IOError as e:
            raise PluginParseError(f"读取配置文件错误: {str(e)}")
        except KeyError as e:
            raise PluginParseError(f"配置文件缺少必要字段: {str(e)}")
        except Exception as e:
            raise PluginParseError(f"解析配置文件失败: {str(e)}")
    
    @staticmethod
    def validate_config(config: Dict[str, Any]) -> None:
        """
        验证配置文件格式是否正确
        
        Args:
            config: 配置字典
            
        Raises:
            PluginParseError: 验证失败时抛出
        """
        required_fields = {
            'plugin_code': str,
            'plugin_version': str,
            'name': str,
            'type': str,
            'engine': str,
            'input_file_type': list,
            'input_media_type': list,
            'classes': list,
        }
        
        for field, field_type in required_fields.items():
            if field not in config:
                raise PluginParseError(f"配置中缺少必填字段: {field}")
                
            if not isinstance(config[field], field_type):
                raise PluginParseError(f"字段 {field} 的类型错误, 应为 {field_type.__name__}")
        
        # 验证枚举值
        if config['type'] not in [t.value for t in PluginType]:
            raise PluginParseError(f"不支持的插件类型: {config['type']}")
            
        # 转换为小写进行验证
        if config['engine'].lower() not in [e.value.lower() for e in EngineType]:
            raise PluginParseError(f"不支持的引擎类型: {config['engine']}")
            
        # 验证列表项
        for file_type in config['input_file_type']:
            if file_type not in [f.value for f in FileType]:
                raise PluginParseError(f"不支持的文件类型: {file_type}")
                
        for media_type in config['input_media_type']:
            if media_type not in [m.value for m in MediaType]:
                raise PluginParseError(f"不支持的媒体类型: {media_type}")
                
        if not config['classes']:
            raise PluginParseError(f"类别列表不能为空")
    
    @staticmethod
    def validate_python_file(python_file_path: str, entry_class_name: str = None) -> None:
        """
        验证Python处理逻辑文件是否符合要求
        
        Args:
            python_file_path: Python文件路径
            entry_class_name: 入口类名称，如果为None则尝试自动发现
            
        Raises:
            PluginParseError: 验证失败时抛出
        """
        try:
            # 获取文件名
            module_name = os.path.basename(python_file_path).replace(".py", "")
            
            # 加载模块
            spec = importlib.util.spec_from_file_location(module_name, python_file_path)
            if not spec or not spec.loader:
                raise PluginParseError(f"无法加载Python模块: {python_file_path}")
                
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # 如果提供了入口类名称，直接查找该类
            if entry_class_name:
                if not hasattr(module, entry_class_name):
                    raise PluginParseError(f"未找到指定的入口类: {entry_class_name}")
                plugin_class = getattr(module, entry_class_name)
            else:
                # 否则尝试自动发现插件类
                plugin_class = None
                for attr_name in dir(module):
                    if attr_name.startswith("__"):
                        continue
                    attr = getattr(module, attr_name)
                    if isinstance(attr, type) and attr.__module__ == module.__name__:
                        if hasattr(attr, "process"):
                            plugin_class = attr
                            break
                
                if not plugin_class:
                    raise PluginParseError("未在Python模块中找到有效的插件类")
            
            # 检查必要的接口方法
            required_methods = ['process']
            for method in required_methods:
                if not hasattr(plugin_class, method) or not callable(getattr(plugin_class, method)):
                    raise PluginParseError(f"插件类缺少必要的方法: {method}")
                    
        except ImportError as e:
            raise PluginParseError(f"导入Python模块错误: {str(e)}")
        except Exception as e:
            raise PluginParseError(f"验证Python文件失败: {str(e)}")
    
    @classmethod
    def validate_plugin_files(
        cls,
        config_file_path: str,
        python_file_path: str
    ) -> None:
        """
        验证插件文件是否存在
        
        Args:
            config_file_path: 配置文件路径
            python_file_path: Python处理逻辑文件路径
            
        Raises:
            PluginParseError: 验证失败时抛出
        """
        # 检查是否为绝对路径，如果不是则转换为绝对路径
        config_file_path = to_absolute_path(config_file_path)
        python_file_path = to_absolute_path(python_file_path)

        # 验证配置文件和Python文件是否存在
        if not os.path.exists(config_file_path):
            raise PluginParseError(f"配置文件不存在: {config_file_path}")
            
        if not os.path.exists(python_file_path):
            raise PluginParseError(f"Python文件不存在: {python_file_path}")
            
        # 验证文件扩展名
        if not python_file_path.endswith('.py'):
            raise PluginParseError(f"Python文件必须以.py为扩展名")
    
    @classmethod
    def parse_plugin(
        cls,
        model_file_path: str,
        config_file_path: str,
        python_file_path: str
    ) -> Dict[str, Any]:
        """
        解析插件文件，返回插件创建模型
        
        Args:
            model_file_path: 模型文件路径
            config_file_path: 配置文件路径
            python_file_path: Python处理逻辑文件路径
            
        Returns:
           Dict[str, Any]: 插件创建模型
            
        Raises:
            PluginParseError: 解析错误时抛出
        """
        # 验证文件
        cls.validate_plugin_files(config_file_path, python_file_path)
        
        # 解析配置文件
        config = cls.parse_config_file(config_file_path)
        
        # 验证配置
        cls.validate_config(config)
        
        # 从配置中获取入口类名称
        entry_class_name = config.get('metadata', {}).get('entry_class')
        
        # 验证Python文件
        cls.validate_python_file(python_file_path, entry_class_name)

        # 检查插件编码是否以 _vlm 结尾
        plugin_code = config.get('plugin_code', '')
        is_vlm_plugin = plugin_code.endswith('_vlm')
        
        # 如果是 VLM 插件，不需要模型文件
        if not is_vlm_plugin:
            model_file_path = to_absolute_path(model_file_path)
            if not os.path.exists(model_file_path):
                raise PluginParseError(f"模型文件不存在: {model_file_path}")

        # 创建插件模型
        return {
            'plugin_code': config['plugin_code'],
            'plugin_version': config['plugin_version'],
            'name': config['name'],
            'description': config.get('description'),
            'type': config['type'],
            'engine': config['engine'],
            'input_file_type': config['input_file_type'],
            'input_media_type': config['input_media_type'],
            'classes': config['classes'],
            'author': config.get('author'),
            'status': config.get('status', 'enabled'),
        }

    @staticmethod
    def _find_plugin_files(plugin_dir: str) -> Tuple[Optional[str], str, str]:
        """
        在插件目录中查找必要的文件
        
        Args:
            plugin_dir: 插件目录路径
            
        Returns:
            Tuple[Optional[str], str, str]: 模型文件（可选）、配置文件和Python文件的路径
            
        Raises:
            PluginParseError: 找不到必要文件时抛出
        """
        # 获取目录中的所有文件
        files = [f for f in os.listdir(plugin_dir) if os.path.isfile(os.path.join(plugin_dir, f))]

        # 查找YAML配置文件
        config_files = [f for f in files if f.endswith('.yaml') or f.endswith('.yml')]
        if not config_files:
            raise PluginParseError(f"在目录 {plugin_dir} 中找不到YAML配置文件")
        config_path = os.path.join(plugin_dir, config_files[0])

        # 查找Python文件
        python_files = [f for f in files if f.endswith('.py')]
        if not python_files:
            raise PluginParseError(f"在目录 {plugin_dir} 中找不到Python文件")
        python_path = os.path.join(plugin_dir, python_files[0])

        # 先解析配置文件，检查是否是 VLM 插件
        with open(config_path, 'rb') as f:
            content = f.read().decode('utf-8')
            config = yaml.safe_load(content)
            plugin_code = config.get('metadata', {}).get('id', '')
            is_vlm_plugin = plugin_code.endswith('_vlm')

        # 如果是 VLM 插件，不需要查找模型文件
        if is_vlm_plugin:
            return None, config_path, python_path

        # 查找模型文件（除了YAML和Python文件以外的最大文件）
        other_files = [f for f in files if not f.endswith('.py') and not f.endswith('.yaml') and not f.endswith('.yml')]
        if not other_files:
            raise PluginParseError(f"在目录 {plugin_dir} 中找不到模型文件")

        # 按文件大小排序，选择最大的文件作为模型文件
        other_files.sort(key=lambda f: os.path.getsize(os.path.join(plugin_dir, f)), reverse=True)
        model_path = os.path.join(plugin_dir, other_files[0])

        return model_path, config_path, python_path