"""
SQLAlchemy 日志监听器模块 - 捕获SQLAlchemy查询并集成traceId
"""
import time
import json
from typing import Any, Dict, List, Union, Tuple, Optional
from sqlalchemy import event
from sqlalchemy.engine import Engine

from app.log.logging import app_logger as logger


class SQLAlchemyLogger:
    """
    SQLAlchemy日志监听器
    
    监听SQLAlchemy引擎的before_cursor_execute和after_cursor_execute事件，
    记录SQL执行信息，并添加traceId (trace_id will be auto-injected by <PERSON><PERSON><PERSON>'s patcher)
    """
    
    def __init__(self):
        """初始化监听器"""
        self._initialized = False
        self._execution_context = {}  # 用于存储语句执行上下文，如开始时间等

    def _format_param_value(self, param: Any) -> str:
        """
        格式化单个参数值为字符串
        
        Args:
            param: 参数值
            
        Returns:
            str: 格式化后的参数值字符串
        """
        if param is None:
            return "NULL"
        elif isinstance(param, (int, float)):
            return str(param)
        elif isinstance(param, str):
            # 转义单引号并用单引号包裹
            escaped = param.replace("'", "''")
            return f"'{escaped}'"
        elif isinstance(param, (list, tuple)):
            formatted_items = [self._format_param_value(item) for item in param]
            return f"({', '.join(formatted_items)})"
        elif isinstance(param, dict):
            try:
                # 尝试使用json序列化，更可靠地处理复杂字典
                return f"'{json.dumps(param, ensure_ascii=False)}'"
            except (TypeError, ValueError):
                # 回退到简单的键值对格式
                formatted_items = [f"{k}={self._format_param_value(v)}" for k, v in param.items()]
                return f"({', '.join(formatted_items)})"
        elif isinstance(param, bool):
            return "TRUE" if param else "FALSE"
        elif hasattr(param, '__str__'):
            # 尝试使用对象的字符串表示，并用单引号包裹
            return f"'{str(param)}'"
        else:
            # 最后的回退，尝试直接转换为字符串
            return f"'{param}'"

    def _format_sql_params(self, sql: str, params: Any) -> str:
        """格式化SQL参数，生成可直接执行的SQL语句
        
        Args:
            sql: SQL语句
            params: 参数值
            
        Returns:
            str: 格式化后的SQL语句
        """
        # 移除SQL语句中的所有换行符和多余空格，保留基本格式
        sql = ' '.join(line.strip() for line in sql.split('\n'))
        
        # 如果没有参数，直接返回SQL语句
        if params is None:
            return sql
            
        # 记录原始参数，用于错误处理
        original_params = params
        
        # 检查SQL语句中是否包含参数占位符
        has_percent_placeholder = '%s' in sql or '%d' in sql or '%f' in sql
        has_dollar_placeholder = any(f"${i}" in sql for i in range(1, 100))  # 检查$1到$99
        has_named_placeholder = ':' in sql and isinstance(params, dict)  # 检查命名参数
        
        # 如果没有识别到的占位符，返回SQL语句和参数信息
        if not (has_percent_placeholder or has_dollar_placeholder or has_named_placeholder):
            # 尝试简单地将参数附加到SQL语句后面以提供更多信息
            param_str = str(params)
            if len(param_str) > 1000:  # 限制参数字符串长度
                param_str = param_str[:1000] + "... (truncated)"
            return f"{sql} /* params: {param_str} */"
        
        try:
            # 处理不同类型的参数占位符
            if has_percent_placeholder:
                # 处理 %s, %d, %f 等格式的占位符
                if isinstance(params, (list, tuple)):
                    formatted_params = tuple(self._format_param_value(p) for p in params)
                else:
                    formatted_params = (self._format_param_value(params),)
                
                # 使用字符串替换而不是 % 操作符，以避免格式化错误
                result = sql
                for i, param in enumerate(formatted_params):
                    # 查找并替换第一个 %s, %d, %f 等占位符
                    for placeholder in ['%s', '%d', '%f', '%r']:
                        if placeholder in result:
                            result = result.replace(placeholder, str(param), 1)
                            break
                return result
                
            elif has_dollar_placeholder:
                # 处理 $1, $2 格式的占位符
                result = sql
                if isinstance(params, (list, tuple)):
                    for i, param in enumerate(params, 1):
                        formatted_param = self._format_param_value(param)
                        # 替换 $1::TYPE 格式的占位符
                        result = result.replace(f"${i}::", f"{formatted_param}::")
                        # 替换 $1 格式的占位符
                        result = result.replace(f"${i}", str(formatted_param))
                else:
                    # 单个参数的情况
                    formatted_param = self._format_param_value(params)
                    result = result.replace("$1::", f"{formatted_param}::")
                    result = result.replace("$1", str(formatted_param))
                return result
                
            elif has_named_placeholder and isinstance(params, dict):
                # 处理 :name 格式的命名参数
                result = sql
                for key, value in params.items():
                    formatted_value = self._format_param_value(value)
                    result = result.replace(f":{key}", str(formatted_value))
                return result
                
            # 如果没有匹配任何已知的占位符格式，返回原始SQL
            return sql
            
        except Exception as e:
            # 如果格式化过程中出现任何错误，返回原始SQL和错误信息
            # 将参数转换为字符串，并限制长度以避免日志过大
            param_str = str(original_params)
            if len(param_str) > 500:  # 限制参数字符串长度
                param_str = param_str[:500] + "... (truncated)"
            
            return f"{sql} /* 参数格式化错误: {str(e)}, 原始参数: {param_str} */"
    
    def initialize(self, engine: Engine) -> None:
        """
        初始化监听器，注册事件
        
        Args:
            engine: SQLAlchemy引擎实例
        """
        if self._initialized:
            return
        
        # 注册before_cursor_execute事件监听，在执行SQL前调用
        event.listen(
            engine, 
            'before_cursor_execute', 
            self._before_cursor_execute
        )
        
        # 注册after_cursor_execute事件监听，在执行SQL后调用
        event.listen(
            engine, 
            'after_cursor_execute', 
            self._after_cursor_execute
        )

        logger.info("SQLAlchemy日志监听器已初始化")
        self._initialized = True
    
    def _before_cursor_execute(self, conn, cursor, statement, 
                              parameters, context, executemany):
        """
        在执行SQL前记录开始时间
        
        Args:
            conn: 数据库连接
            cursor: 游标
            statement: SQL语句
            parameters: 绑定参数
            context: 执行上下文
            executemany: 是否是executemany操作
        """
        # 使用连接的哈希值作为唯一标识符
        conn_id = id(conn)
        
        # 记录开始时间
        self._execution_context[conn_id] = {
            'start_time': time.time(),
            'statement': statement,
            'parameters': parameters
        }
    
    def _after_cursor_execute(self, conn, cursor, statement, 
                             parameters, context, executemany):
        """
        在执行SQL后记录执行时间并输出日志
        
        Args:
            conn: 数据库连接
            cursor: 游标
            statement: SQL语句
            parameters: 绑定参数
            context: 执行上下文
            executemany: 是否是executemany操作
        """
        conn_id = id(conn)
        
        # 检查连接ID是否存在于上下文中
        if conn_id in self._execution_context:
            # 计算执行时间
            start_time = self._execution_context[conn_id]['start_time']
            execution_time = (time.time() - start_time) * 1000  # 毫秒
            
            # 获取SQL语句和参数
            sql = self._execution_context[conn_id]['statement']
            params = self._execution_context[conn_id]['parameters']
            
            # 格式化SQL语句
            formatted_sql = self._format_sql_params(sql, params)
            
            # executemany 表示批量执行SQL操作，如批量插入、批量更新等
            execute_type = 'executemany' if executemany else 'execute'
            
            # 使用非JSON格式记录SQL日志
            logger.info(f"SQL耗时={round(execution_time, 2)}ms, SQL语句={formatted_sql}")
            
            # 清理上下文
            del self._execution_context[conn_id]


# 创建单例实例
sql_logger = SQLAlchemyLogger() 