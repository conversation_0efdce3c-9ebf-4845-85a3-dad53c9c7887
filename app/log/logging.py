import logging
import os
import sys
from typing import Dict, Optional
from pathlib import Path
from loguru import logger

from app.config.settings import settings
from app.utils.trace_context_utils import get_trace_id

# 全局SINK ID存储，用于动态配置
SINK_IDS: Dict[str, int] = {}

# --- 统一日志格式 ---
# 带trace_id的日志格式（控制台带颜色，文件不带颜色）
LOG_FORMAT_WITH_TRACE_ID = (
    "{time:YYYY-MM-DD HH:mm:ss.SSS} | "
    "{level: <8} | "
    "{extra[trace_id]} | "
    "{name}:{function}:{line} - {message}\n"
)

# 不带trace_id的日志格式（控制台带颜色，文件不带颜色）
LOG_FORMAT_WITHOUT_TRACE_ID = (
    "{time:YYYY-MM-DD HH:mm:ss.SSS} | "
    "{level: <8} | "
    "{name}:{function}:{line} - {message}\n"
)

def dynamic_format(record: Dict) -> str:
    """根据是否存在trace_id动态选择日志格式"""
    # 如果有 trace_id 并且不为 "N/A"，使用带 trace_id 的格式
    # 否则使用不带 trace_id 的格式
    # 注意：这里的条件逻辑设计确保不会因 None 或空字符串出现问题
    trace_id = record["extra"].get("trace_id", "N/A")
    if trace_id and trace_id != "N/A":
        # 始终确保使用带 trace_id 的格式
        return LOG_FORMAT_WITH_TRACE_ID
    # 否则使用不带 trace_id 的格式
    return LOG_FORMAT_WITHOUT_TRACE_ID

# --- Trace ID Patcher ---
def trace_id_patcher(record: Dict) -> None:
    """将trace_id注入到日志记录的extra中"""
    trace_id = get_trace_id() or "N/A"
    # 为了确保 extra["trace_id"] 始终有值，并一直使用带 trace_id 的格式
    record["extra"]["trace_id"] = trace_id

# --- 标准库日志拦截处理器 ---
class InterceptHandler(logging.Handler):
    """
    Custom logging handler to intercept standard logging messages and redirect them to Loguru.
    This allows libraries using standard logging to have their logs processed by Loguru.
    """

    def emit(self, record: logging.LogRecord):
        """
        Handles a LogRecord from the standard logging system.
        """
        # 跳过低级别日志，减少拦截
        if record.levelno < logging.ERROR:  # 只拦截ERROR及以上级别的日志
            return
            
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = record.levelno  # type: ignore

        frame = logging.currentframe()
        depth = 0
        # Rewind to find the frame outside of the logging module for accurate call site info in Loguru
        while frame and frame.f_code.co_filename == logging.__file__:
            frame = frame.f_back
            depth += 1
        if frame is None:  # Fallback depth if something goes wrong
            depth = 0

        # Prepare context from the standard LogRecord to be patched into Loguru's record
        loguru_context_patch = {
            "_name": record.name,
            "_module": record.module,
            "_file": record.pathname,
            "_line": record.lineno,
            "_func": record.funcName,
            # Map other potentially useful LogRecord attributes if needed
            # e.g., "_thread_id": record.thread, "_thread_name": record.threadName
        }

        # Patch the context into the logger and then log the message
        # This avoids passing arbitrary kwargs to .log() which caused the TypeError
        patched_logger = logger.patch(lambda r: r.update(loguru_context_patch))
        patched_logger.opt(depth=depth, exception=record.exc_info).log(level, record.getMessage())

# --- 日志设置与配置函数 ---
def setup_logging() -> None:
    """
    初始化并配置Loguru日志系统。
    """
    global SINK_IDS

    # 1. 移除所有现有的 Loguru 处理器，确保从干净的状态开始
    logger.remove()

    # 2. 应用 Trace ID Patcher
    # 确保在添加任何 sink 前先应用 patcher
    logger.configure(patcher=trace_id_patcher)

    log_level = "DEBUG" if settings.DEBUG else "INFO"

    # 3. 配置控制台 Sink
    try:
        console_sink_id = logger.add(
            sys.stderr,
            level=log_level,
            format=dynamic_format,
            colorize=True,
            enqueue=True, # 异步、多进程安全
            backtrace=True, # 更好的异常追溯
            diagnose=settings.DEBUG # DEBUG模式下提供更详细的诊断信息
        )
        SINK_IDS["console"] = console_sink_id
    except Exception as e:
        # 在非常早期的启动阶段，如果stderr有问题，至少尝试打印到stdout
        print(f"Error configuring console stderr sink for Loguru: {e}, falling back to stdout if possible.", file=sys.stdout)
        try:
            console_sink_id = logger.add(
                sys.stdout, # Fallback
                level=log_level,
                format=dynamic_format,
                colorize=True,
                enqueue=True,
                backtrace=True,
                diagnose=settings.DEBUG
            )
            SINK_IDS["console"] = console_sink_id
        except Exception as e_stdout:
            print(f"Error configuring console stdout sink for Loguru: {e_stdout}", file=sys.stdout)


    # 4. 配置文件 Sink
    try:
        # 创建日志目录
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        log_file_path = os.path.join(log_dir, "app.log")

        file_sink_id = logger.add(
            log_file_path,  # 使用带有日期格式的文件路径
            level=log_level,
            format=dynamic_format,
            rotation=settings.LOG_FILE_ROTATION,  # 每天午夜轮转
            retention=settings.LOG_FILE_RETENTION_POLICY,  # 保留7天的日志
            encoding="utf-8",
            enqueue=True, # 异步、多进程安全
            backtrace=True, # 显示异常回溯
            diagnose=settings.DEBUG
        )
        SINK_IDS["file"] = file_sink_id
    except Exception as e:
        # 如果文件sink配置失败，打印错误到控制台
        logger.error(f"Failed to configure file sink for Loguru: {e}")


    # 5. 配置标准库日志拦截
    # 只拦截特定的日志源，而不是所有
    # 使用更高的日志级别，减少日志量
    
    # 仅配置根日志记录器，而不是所有日志记录器
    root_logger = logging.getLogger()
    root_logger.handlers = [InterceptHandler()]
    root_logger.setLevel(logging.ERROR)  # 只拦截ERROR及以上级别的日志
    
    # 根据配置设置特定第三方库的日志级别
    # Uvicorn (access and error logs are often handled by FastAPI/Starlette middlewares)
    logging.getLogger("uvicorn").setLevel(logging.ERROR)
    logging.getLogger("uvicorn.error").setLevel(logging.ERROR)
    logging.getLogger("uvicorn.access").setLevel(logging.ERROR) # 通常由 access_log_middleware 处理

    logger.info(f"Loguru logging system initialized. Level: {log_level}")
    logger.info(f"File rotation: {settings.LOG_FILE_ROTATION}, Retention: {settings.LOG_FILE_RETENTION_POLICY}")

    # 6. 添加OpenObserve集成
    try:
        from app.telemetry.openobserve import openobserve_integration
        otlp_handler = openobserve_integration.setup()

        if otlp_handler:
            # 创建OpenObserve sink
            openobserve_sink = openobserve_integration.create_loguru_sink(otlp_handler)

            # 添加OpenObserve sink到Loguru
            openobserve_sink_id = logger.add(
                openobserve_sink,
                level="INFO",  # 只发送INFO及以上级别的日志到OpenObserve
                format="{message}",  # 使用简单格式，详细信息在extra中
                enqueue=True,  # 异步处理
                catch=True     # 捕获sink内部异常
            )
            SINK_IDS["openobserve"] = openobserve_sink_id

            logger.info("OpenObserve日志集成已启用")
        else:
            logger.warning("OpenObserve集成未启用，请检查环境变量配置")

    except ImportError:
        logger.warning("OpenObserve集成模块未找到，跳过集成")
    except Exception as e:
        logger.error(f"OpenObserve集成失败: {e}")

    # 7. 添加OpenTelemetry日志集成
    try:
        # 在多进程环境下，每个进程都需要独立初始化OpenTelemetry
        from app.telemetry.openobserve import setup_openobserve_integration
        log_handler, trace_enabled = setup_openobserve_integration()

        if trace_enabled:
            logger.info("OpenTelemetry日志集成已启用")
        else:
            logger.warning("OpenTelemetry未启用")
    except ImportError:
        logger.warning("OpenTelemetry日志集成模块未找到，跳过集成")
    except Exception as e:
        logger.error(f"OpenTelemetry日志集成失败: {e}")

    # ===== SQLAlchemy 日志控制 =====

    # 与 setup_logging 保持一致的 SQLAlchemy 日志控制

    # 1. 完全禁用原生的 SQLAlchemy 所有日志源
    # SQLAlchemy 顶级日志记录器 
    logging.getLogger("sqlalchemy").setLevel(logging.CRITICAL)  # 抑制所有SQLAlchemy日志的顶级控制

    # 2. 启用我们自己的 SQL 日志记录器
    logging.getLogger("app.core.sql_logger").setLevel(logging.DEBUG)

def get_logger(name: Optional[str] = None): # -> "loguru.Logger" but avoid cyclic import for type hint
    """
    获取一个logger实例。
    如果提供了名称，则返回一个绑定了该名称的logger，可用于在日志格式中通过 {extra[name]} 区分。
    否则返回全局logger。
    """
    if name:
        return logger.bind(name=name)
    return logger

# 创建各模块日志记录器
app_logger = get_logger("app")
task_logger = get_logger("task")
plugin_logger = get_logger("plugin")
file_logger = get_logger("file")
cache_logger = get_logger("cache")

# --- 初始化日志系统 ---
# 在模块加载时自动执行设置
try:
    setup_logging()
except Exception as e:
    # Fallback to basic print if initial logging setup fails catastrophically
    print(f"CRITICAL: Initial Loguru setup_logging FAILED: {e}", file=sys.stderr)
    # As a last resort, ensure logger at least has a default handler if everything else failed
    try:
        if not logger._core.handlers: # Check if any handlers exist
            logger.add(sys.stderr, level="DEBUG", format="{time} {level} {message}") # Minimal fallback
            logger.error("Loguru initial setup failed, added minimal stderr fallback handler.")
    except Exception as final_e:
        print(f"CRITICAL: Failed to add minimal Loguru fallback handler: {final_e}", file=sys.stderr)

