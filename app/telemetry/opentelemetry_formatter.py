"""
OpenTelemetry集成的日志格式化器
自动从OpenTelemetry context中提取trace信息并注入到日志中
"""
import json
import sys
import time
from typing import Dict, Any, Optional

from loguru import logger
from opentelemetry import trace
from opentelemetry.trace import Status, StatusCode


class OpenTelemetryLogFormatter:
    """OpenTelemetry集成的日志格式化器"""
    
    def __init__(self, service_name: str = "ai-platform"):
        self.service_name = service_name
    
    def format_record(self, record: Dict[str, Any]) -> Dict[str, Any]:
        """格式化日志记录，自动注入OpenTelemetry trace信息"""
        # 获取当前的OpenTelemetry context
        current_span = trace.get_current_span()
        
        # 基础日志信息
        formatted_record = {
            "timestamp": record["time"].isoformat(),
            "level": record["level"].name,
            "logger": record["name"],
            "message": record["message"],
            "service": self.service_name,
        }
        
        # 添加OpenTelemetry trace信息
        if current_span and current_span.is_recording():
            span_context = current_span.get_span_context()
            
            # 添加trace信息
            formatted_record.update({
                "trace_id": f"{span_context.trace_id:032x}",
                "span_id": f"{span_context.span_id:016x}",
                "trace_flags": span_context.trace_flags,
            })
            
            # 获取span名称（如果可能）
            if hasattr(current_span, '_name'):
                formatted_record["span_name"] = current_span._name
            
            # 获取parent span信息（如果有）
            if hasattr(current_span, '_parent') and current_span._parent:
                formatted_record["parent_span_id"] = f"{current_span._parent.span_id:016x}"
        
        # 添加额外的字段
        if "extra" in record and record["extra"]:
            formatted_record.update(record["extra"])
        
        # 添加文件和行号信息
        if "file" in record:
            formatted_record["source"] = {
                "file": record["file"].name,
                "line": record["line"],
                "function": record["function"]
            }
        
        # 添加异常信息
        if record.get("exception"):
            formatted_record["exception"] = {
                "type": record["exception"].type.__name__,
                "message": str(record["exception"].value),
                "traceback": record["exception"].traceback
            }
        
        return formatted_record


class OpenTelemetryLogHandler:
    """OpenTelemetry集成的日志处理器"""
    
    def __init__(self, service_name: str = "ai-platform"):
        self.formatter = OpenTelemetryLogFormatter(service_name)
    
    def __call__(self, record):
        """处理日志记录"""
        try:
            # 格式化记录
            formatted_record = self.formatter.format_record(record)

            # 输出为JSON格式
            json_output = json.dumps(formatted_record, ensure_ascii=False, default=str)

            # 返回格式化的字符串
            return json_output + "\n"

        except Exception as e:
            # 如果格式化失败，返回原始消息
            try:
                return f"{record.get('time', 'N/A')} | {record.get('level', {}).get('name', 'INFO')} | {record.get('message', 'N/A')}\n"
            except:
                return f"LOG_ERROR: {str(e)}\n"


class SpanLogger:
    """Span级别的日志记录器"""
    
    @staticmethod
    def log_span_start(span_name: str, attributes: Optional[Dict[str, Any]] = None):
        """记录span开始"""
        current_span = trace.get_current_span()
        if current_span and current_span.is_recording():
            span_context = current_span.get_span_context()
            
            log_data = {
                "event_type": "span_start",
                "span_name": span_name,
                "trace_id": f"{span_context.trace_id:032x}",
                "span_id": f"{span_context.span_id:016x}",
                "timestamp": time.time()
            }
            
            if attributes:
                log_data["attributes"] = attributes
            
            logger.info(f"SPAN_START: {span_name}", extra=log_data)
    
    @staticmethod
    def log_span_end(span_name: str, status: str = "success", duration_ms: Optional[float] = None, 
                     attributes: Optional[Dict[str, Any]] = None):
        """记录span结束"""
        current_span = trace.get_current_span()
        if current_span and current_span.is_recording():
            span_context = current_span.get_span_context()
            
            log_data = {
                "event_type": "span_end",
                "span_name": span_name,
                "trace_id": f"{span_context.trace_id:032x}",
                "span_id": f"{span_context.span_id:016x}",
                "status": status,
                "timestamp": time.time()
            }
            
            if duration_ms is not None:
                log_data["duration_ms"] = duration_ms
            
            if attributes:
                log_data["attributes"] = attributes
            
            logger.info(f"SPAN_END: {span_name}", extra=log_data)
    
    @staticmethod
    def log_span_event(event_name: str, attributes: Optional[Dict[str, Any]] = None):
        """记录span事件"""
        current_span = trace.get_current_span()
        if current_span and current_span.is_recording():
            span_context = current_span.get_span_context()
            
            log_data = {
                "event_type": "span_event",
                "event_name": event_name,
                "trace_id": f"{span_context.trace_id:032x}",
                "span_id": f"{span_context.span_id:016x}",
                "timestamp": time.time()
            }
            
            if attributes:
                log_data["attributes"] = attributes
            
            # 同时添加到OpenTelemetry span
            current_span.add_event(event_name, attributes or {})
            
            logger.info(f"SPAN_EVENT: {event_name}", extra=log_data)
    
    @staticmethod
    def log_span_error(error: Exception, attributes: Optional[Dict[str, Any]] = None):
        """记录span错误"""
        current_span = trace.get_current_span()
        if current_span and current_span.is_recording():
            span_context = current_span.get_span_context()
            
            log_data = {
                "event_type": "span_error",
                "error_type": type(error).__name__,
                "error_message": str(error),
                "trace_id": f"{span_context.trace_id:032x}",
                "span_id": f"{span_context.span_id:016x}",
                "timestamp": time.time()
            }
            
            if attributes:
                log_data["attributes"] = attributes
            
            # 设置span状态为错误
            current_span.set_status(Status(StatusCode.ERROR, str(error)))
            current_span.record_exception(error)
            
            logger.error(f"SPAN_ERROR: {type(error).__name__}", extra=log_data)






