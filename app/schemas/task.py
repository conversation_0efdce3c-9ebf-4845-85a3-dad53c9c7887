"""
任务管理相关的Schema定义
"""
from datetime import datetime
from typing import List, Optional, Dict, Any
from enum import Enum

from pydantic import Field, field_validator

from app.schemas.base import CamelModel, FileType, MediaType

class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"  # 等待执行
    RUNNING = "running"  # 正在执行
    COMPLETED = "completed"  # 执行完成
    FAILED = "failed"  # 执行失败
    CANCELED = "canceled"  # 已取消


class TaskItemBase(CamelModel):
    """任务项基础模型"""
    data_id: Optional[str] = Field(None, description="外部系统数据ID")
    file_type: FileType = Field(..., description="文件类型")
    media_type: MediaType = Field(None, description="媒体类型")
    file_url: str = Field(..., description="文件URL")
    plugin_code: str = Field(..., description="插件编码")
    plugin_version: str = Field(..., description="插件版本")
    params: Optional[Dict[str, Any]] = Field(None, description="任务项参数")

    @field_validator("params", mode="before")
    @classmethod
    def default_params(cls, v: Optional[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        return v


class PluginConfig(CamelModel):
    """插件配置模型"""
    plugin_code: str = Field(..., description="插件代码")
    plugin_version: str = Field(..., description="插件版本")


class TaskItemCreate(CamelModel):
    """任务项创建模型"""
    data_id: str = Field(..., description="数据ID")
    file_type: FileType = Field(..., description="文件类型")
    media_type: MediaType = Field(None, description="媒体类型")
    file_url: str = Field(..., description="文件URL")
    params: Optional[Dict[str, Any]] = Field(None, description="处理参数")

    @field_validator("params", mode="before")
    @classmethod
    def default_params(cls, v: Optional[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        return v


class TaskItem(CamelModel):
    """任务项模型"""
    task_item_id: int = Field(..., description="任务项ID")
    task_id: int = Field(..., description="任务ID")
    data_id: str = Field(..., description="数据ID")
    file_type: FileType = Field(..., description="文件类型")
    media_type: MediaType = Field(None, description="媒体类型")
    file_id: int = Field(..., description="文件ID")
    file_url: str = Field(..., description="文件URL")
    plugin_code: str = Field(..., description="插件代码")
    plugin_version: str = Field(..., description="插件版本")
    params: Optional[Dict[str, Any]] = Field(None, description="处理参数")
    status: str = Field(..., description="任务项状态")
    error: Optional[str] = Field(None, description="错误信息")
    result: Optional[Dict[str, Any]] = Field(None, description="处理结果")
    from_cache: bool = Field(False, description="是否来自缓存")
    process_time: Optional[Dict[str, Any]] = Field(None, description="处理各阶段时间")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    started_at: Optional[datetime] = Field(None, description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")

    @field_validator("params", mode="before")
    @classmethod
    def default_params(cls, v: Optional[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        return v


class TaskItemUpdate(CamelModel):
    """任务项更新模型"""
    status: Optional[str] = Field(None, description="任务项状态")
    error: Optional[str] = Field(None, description="错误信息")
    result: Optional[Dict[str, Any]] = Field(None, description="处理结果")
    process_time: Optional[Dict[str, Any]] = Field(None, description="处理各阶段时间")
    started_at: Optional[datetime] = Field(None, description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")


class TaskItemResponse(CamelModel):
    """任务项响应模型"""
    task_item_id: int = Field(..., description="任务项ID")
    task_id: int = Field(..., description="任务ID")
    data_id: str = Field(..., description="数据ID")
    file_type: FileType = Field(..., description="文件类型")
    media_type: MediaType = Field(None, description="媒体类型")
    file_hash: Optional[str] = Field(..., description="文件哈希")
    file_url: str = Field(..., description="文件URL")
    status: str = Field(..., description="任务项状态")
    error: Optional[str] = Field(None, description="错误信息")
    plugin_code: str = Field(..., description="插件代码")
    plugin_version: str = Field(..., description="插件版本")
    result: Optional[Dict[str, Any]] = Field(None, description="处理结果")
    started_at: Optional[datetime] = Field(None, description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")


class TaskBase(CamelModel):
    """任务基础模型"""
    priority: int = Field(default=1, ge=1, le=10, description="优先级(1-10)")


class TaskCreate(CamelModel):
    """任务创建模型"""
    priority: int = Field(default=1, ge=1, le=10, description="优先级")
    plugins: List[PluginConfig] = Field(..., description="插件配置列表")
    items: List[TaskItemCreate] = Field(..., description="任务项列表")


class TaskUpdate(CamelModel):
    """任务更新模型"""
    priority: Optional[int] = Field(None, ge=1, le=10, description="优先级")
    status: Optional[str] = Field(None, description="任务状态")
    completed_items: Optional[int] = Field(None, description="已完成的任务项数量")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    started_at: Optional[datetime] = Field(None, description="开始时间")


class TaskResponse(CamelModel):
    """任务响应模型"""
    task_id: int = Field(..., description="任务ID")
    progress: str = Field(..., description="处理进度")
    status: str = Field(..., description="任务状态")
    priority: int = Field(..., description="优先级")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    started_at: Optional[datetime] = Field(None, description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")


class TaskFilter(CamelModel):
    """任务过滤模型"""
    status: Optional[TaskStatus] = Field(None, description="任务状态")
    priority: Optional[int] = Field(None, ge=1, le=10, description="优先级")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间") 