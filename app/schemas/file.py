"""
文件管理相关的Schema定义
"""
from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any
from pydantic import Field

from app.schemas.base import CamelModel, FileType, MediaType


class FileBase(CamelModel):
    """文件基础模型"""
    size: int = Field(..., description="文件大小(字节)")
    file_type: FileType = Field(..., description="文件类型")
    media_type: Optional[str] = Field(None, description="媒体类型")
    file_metadata: Optional[Dict[str, Any]] = Field(None, description="文件元数据")
    content_type: Optional[str] = Field(None, description="MIME类型")


class FileCreate(FileBase):
    """创建文件模型"""
    url: str = Field(..., description="文件访问URL")
    file_hash: str = Field(..., description="文件哈希值")
    is_deleted: bool = Field(False, description="是否已删除")
    local_path: str = Field(None, description="本地存储路径")
    oss_path: str = Field(None, description="OSS存储路径")
    platform: str = Field(..., description="文件来源平台")


class FileInDB(FileBase):
    """数据库中的文件模型"""
    id: int = Field(..., description="文件ID")
    url: str = Field(..., description="文件访问URL")
    file_hash: str = Field(..., description="文件哈希值")
    local_path: Optional[str] = Field(None, description="本地存储路径")
    oss_path: str = Field(..., description="OSS存储路径")
    platform: Optional[str] = Field(None, description="文件来源平台")
    is_deleted: bool = Field(False, description="是否已删除")
    created_at: Optional[datetime] = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(..., description="更新时间")


class File(FileBase):
    """返回给客户端的文件模型"""
    id: int = Field(..., description="文件ID")
    url: str = Field(..., description="文件访问URL")
    file_hash: str = Field(..., description="文件哈希值")
    local_path: Optional[str] = Field("", description="本地存储路径")
    oss_path: str = Field("", description="OSS存储路径")
    created_at: Optional[datetime] = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(..., description="更新时间")


# 请求模型
class FileUploadRequest(CamelModel):
    """文件上传请求参数"""
    file_type: Optional[FileType] = Field(None, description="文件类型")
    generate_metadata: bool = Field(True, description="是否生成元数据")


class FileDownloadRequest(CamelModel):
    """文件下载请求参数"""
    url: str = Field(..., description="文件URL")
    file_type: FileType = Field(None, description="文件类型")


class FileDeleteRequest(CamelModel):
    """文件删除请求参数"""
    pass  # 不再需要任何参数，统一使用逻辑删除


class TempUrlRequest(CamelModel):
    """临时URL请求参数"""
    expires: int = Field(3600, description="过期时间(秒)", ge=1, le=86400)  # 1秒到24小时


class STSToken(CamelModel):
    """STS令牌信息"""
    access_key_id: str = Field(..., description="临时访问密钥ID")
    access_key_secret: str = Field(..., description="临时访问密钥")
    security_token: str = Field(..., description="安全令牌")
    expiration: datetime = Field(..., description="令牌过期时间")


class TempUrlResponse(CamelModel):
    """临时URL响应模型"""
    url: str = Field(..., description="临时访问URL")
    expires_at: datetime = Field(..., description="URL过期时间")
    sts_token: STSToken = Field(..., description="STS令牌信息") 