"""
插件管理相关的Schema模型
"""
from datetime import datetime
from enum import Enum
from typing import List, Optional, Dict, Any
from fastapi import UploadFile

from pydantic import Field, BaseModel

from app.schemas.base import CamelModel, FileType, MediaType


class PluginType(str, Enum):
    """插件类型枚举"""
    DETECTION = "detection"
    QUANTIZATION = "quantization"
    MOSAIC = "mosaic"


class EngineType(str, Enum):
    """推理引擎类型枚举"""
    ONNXRuntime = "onnxruntime"
    PyTorch = "pytorch"
    OpenCV = "opencv"
    Simple = "simple"

class PluginStatus(str, Enum):
    """插件状态枚举"""
    ENABLED = "enabled"
    DISABLED = "disabled"


class PluginSyncRequest(CamelModel):
    """插件同步请求模型"""
    plugin_dir_path: str = Field(..., description="插件目录路径（相对于FILE_STORAGE_PATH的路径）")
    update_if_newer: bool = Field(True, description="是否只在版本更新时更新插件，为false时强制更新")

    class Config:
        json_schema_extra = {
            "example": {
                "pluginDirPath": "ai-platform-plugin/face_detect_mosaic/2.0.0",
                "updateIfNewer": True
            }
        }


class PluginBase(CamelModel):
    """插件基础模型"""
    plugin_code: str = Field(..., description="插件唯一编码")
    plugin_version: str = Field(..., description="插件版本号")
    name: str = Field(..., description="插件名称", min_length=1, max_length=100)
    description: Optional[str] = Field(None, description="插件描述")
    type: PluginType = Field(..., description="插件类型")
    engine: EngineType = Field(..., description="推理引擎类型")
    input_file_type: List[FileType] = Field(..., description="支持的输入文件类型")
    input_media_type: List[MediaType] = Field(..., description="支持的输入媒体类型")
    classes: List[str] = Field(..., description="支持的类别列表")
    author: Optional[str] = Field(None, description="插件作者")
    status: PluginStatus = Field(PluginStatus.ENABLED, description="插件状态")

    model_config = {
        "use_enum_values": True
    }


class PluginCreate(PluginBase):
    """插件创建模型"""
    pass


class PluginUpdate(CamelModel):
    """插件更新模型"""
    name: Optional[str] = Field(None, description="插件名称", min_length=1, max_length=100)
    description: Optional[str] = Field(None, description="插件描述")
    status: Optional[PluginStatus] = Field(None, description="插件状态")

    model_config = {
        "use_enum_values": True
    }


class PluginResponse(PluginBase):
    """插件详情返回模型"""
    id: int = Field(..., description="插件ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    model_config = {
        "from_attributes": True,
        "use_enum_values": True
    }


class PluginFilter(CamelModel):
    """插件查询过滤条件"""
    plugin_code: Optional[str] = Field(None, description="插件编码(模糊匹配)")
    name: Optional[str] = Field(None, description="插件名称(模糊匹配)")
    type: Optional[PluginType] = Field(None, description="插件类型")
    engine: Optional[EngineType] = Field(None, description="推理引擎类型")
    status: Optional[PluginStatus] = Field(None, description="插件状态")
    file_type: Optional[FileType] = Field(None, description="文件类型")
    media_type: Optional[MediaType] = Field(None, description="媒体类型")
    
    model_config = {
        "use_enum_values": True
    }


class PluginInfo(BaseModel):
    # 核心标识
    plugin_code: str
    plugin_version: str
    
    # 引擎信息
    engine: str = "simple"
    model_file_path: Optional[str] = None
    
    # 文件路径
    python_file_path: Optional[str] = None
    config_file_path: Optional[str] = None

    # 元信息
    name: Optional[str] = None
    type: Optional[str] = None
    
    class Config:
        from_attributes = True  # 从数据库模型转换时使用 