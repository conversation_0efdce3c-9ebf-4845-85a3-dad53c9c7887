"""
缓存Pydantic模型
"""
from datetime import datetime
from typing import Dict, Any, Optional

from pydantic import BaseModel, Field


class ResultCacheBase(BaseModel):
    """缓存基础模型"""
    file_hash: str = Field(..., description="文件哈希")
    plugin_code: str = Field(..., description="插件编码")
    plugin_version: str = Field(..., description="插件版本")
    params_hash: str = Field(..., description="参数哈希")
    result: Dict[str, Any] = Field(..., description="缓存结果")
    process_time: Dict[str, Any] = Field(..., description="处理各阶段时间")


class ResultCacheCreate(ResultCacheBase):
    """缓存创建模型"""
    id: int = Field(..., description="雪花ID")


class ResultCacheUpdate(BaseModel):
    """缓存更新模型"""
    result: Optional[Dict[str, Any]] = Field(None, description="缓存结果")
    process_time: Optional[Dict[str, Any]] = Field(None, description="处理各阶段时间")


class ResultCache(ResultCacheBase):
    """缓存响应模型"""
    id: int = Field(..., description="雪花ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True 