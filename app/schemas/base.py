"""
基础数据模型Schema定义
"""
from datetime import datetime, timedelta, timezone
from enum import Enum
from typing import Any, Dict, Generic, List, Optional, TypeVar, Union
import uuid
import time

from pydantic import BaseModel, Field, ConfigDict
from app.utils.naming import snake_to_camel, camel_to_snake
from app.utils.trace_context_utils import get_trace_id

# 定义泛型数据类型
DataT = TypeVar('DataT')

def format_datetime_with_timezone(dt: datetime) -> str:
    """格式化日期时间为东八区 YYYY-MM-DD HH:MM:SS 格式"""
    # 定义东八区时区
    tz_cn = timezone(timedelta(hours=8))
    
    # 如果日期时间没有时区信息，假定为UTC，然后转换到东八区
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=timezone.utc)
    
    # 转换到东八区
    dt_cn = dt.astimezone(tz_cn)
    
    # 格式化为指定格式
    return dt_cn.strftime("%Y-%m-%d %H:%M:%S")


class CamelModel(BaseModel):
    """
    基础模型类，将Pydantic模型的所有字段从snake_case转为camelCase
    
    在HTTP请求和响应中使用驼峰形式，而在内部使用蛇形形式
    """
    model_config = ConfigDict(
        populate_by_name=True,  # 允许通过属性名称填充
        alias_generator=snake_to_camel,  # 自动生成小驼峰别名
        json_encoders={
            datetime: format_datetime_with_timezone,  # 自定义日期时间格式化
            int: lambda n: str(n) if n > 9007199254740991 or n < -9007199254740991 else n  # 处理超出 JS 安全整数范围的数值
        }
    )


class BaseResponse(CamelModel, Generic[DataT]):
    """统一响应模型"""
    code: int = Field(default=0, description="状态码，0表示成功，其他表示错误")
    success: bool = Field(default=True, description="是否成功")
    message: str = Field(default="操作成功", description="操作结果描述")
    data: Optional[DataT] = Field(default=None, description="响应数据")
    trace_id: str = Field(default_factory=lambda: get_trace_id() or str(uuid.uuid4()), description="请求追踪ID")
    timestamp: str = Field(default_factory=lambda: str(int(time.time() * 1000)), description="响应时间戳（毫秒）")


class ErrorResponse(BaseResponse[None]):
    """错误响应模型"""
    success: bool = False
    data: None = None


class PageResponse(CamelModel, Generic[DataT]):
    """
    泛型分页数据模型
    """
    page: int = Field(description="当前页码")
    page_size: int = Field(description="每页记录数")
    total_count: int = Field(description="总记录数")
    total_page: int = Field(description="总页数")
    items: List[DataT] = Field(description="数据列表")


class PageParams(CamelModel):
    """
    分页查询参数
    """
    page: int = Field(default=1, ge=1, description="页码，从1开始")
    page_size: int = Field(default=10, ge=1, le=200, description="每页记录数")


class TimeFilter(CamelModel):
    """
    时间范围过滤器
    """
    start_time: Optional[datetime] = Field(default=None, description="开始时间")
    end_time: Optional[datetime] = Field(default=None, description="结束时间")


class SortOrder(str):
    """排序方向"""
    ASC = "asc"
    DESC = "desc"


class SortParams(CamelModel):
    """
    排序参数
    """
    field: str = Field(description="排序字段")
    order: str = Field(description="排序方向", default=SortOrder.ASC)


class FileType(str, Enum):
    """文件类型枚举"""
    IMAGE = "image"
    VIDEO = "video"
    AUDIO = "audio"
    TEXT = "text"

class MediaType(str, Enum):
    """媒体类型枚举"""
    VISIBLE_LIGHT = "visible_light"  # 可见光
    INFRARED_LIGHT = "infrared_light"  # 红外光
    FACE_MOSAIC = "face_mosaic"  # 人脸
    OTHER = "other"  # 其他