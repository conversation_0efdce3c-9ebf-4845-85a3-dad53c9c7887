"""
推理引擎接口定义模块
包含所有引擎必须实现的基础接口
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional


class InferenceEngine(ABC):
    """
    推理引擎接口，所有具体引擎必须实现此接口
    
    定义了引擎的标准生命周期和能力：
    1. initialize: 初始化引擎和加载模型
    2. get_client: 获取引擎客户端
    3. destroy: 销毁资源
    """
    
    @abstractmethod
    async def initialize(self, model_path: str, config: Dict[str, Any]) -> None:
        """
        初始化引擎及模型
        
        Args:
            model_path: 模型文件路径
            config: 配置参数
        
        Raises:
            EngineInitException: 初始化失败时抛出
            ModelLoadException: 模型加载失败时抛出
        """
        pass
    
    @abstractmethod
    async def get_client(self) -> Dict[str, Any]:
        """
        获取引擎客户端
        
        Returns:
            引擎客户端对象，如ONNX的session或API的HTTP客户端
            
        Raises:
            EngineInitException: 客户端获取失败时抛出
        """
        pass
        
    @abstractmethod
    async def destroy(self) -> None:
        """
        销毁资源
        
        在不再使用引擎时调用，释放内存和其他资源
        """
        pass 