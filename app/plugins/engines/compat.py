"""
推理引擎兼容性适配器模块
提供对不同插件接口约定的兼容支持和配置管理
"""
from typing import Any, Callable, Dict
import asyncio
import os
import yaml

from app.config.settings import settings

# 默认配置
DEFAULT_CONFIG = {
    "idle_timeout": 1800,             # 闲置超时时间(秒)
    "cleanup_interval": 300,          # 清理检查间隔(秒)
    "max_instances": None,           # 最大实例数，None表示不限制
    "default_engine_config": {
        "onnxruntime": {
            "execution_provider": ["CUDAExecutionProvider", "CPUExecutionProvider"],  # 优先使用CUDA，自动降级到CPU
            "inter_op_num_threads": 4,
            "intra_op_num_threads": 4
        }
    },
    "plugin_base_path": os.path.join(settings.STORAGE_PATH, settings.PLUGIN_STORAGE_PATH),  # 使用新的路径组合方式
}

def _deep_update(original: Dict, update: Dict) -> Dict:
    """
    深度更新字典
    
    Args:
        original: 原始字典
        update: 更新字典
        
    Returns:
        更新后的字典
    """
    for key, value in update.items():
        if key in original and isinstance(original[key], dict) and isinstance(value, dict):
            _deep_update(original[key], value)
        else:
            original[key] = value
    return original


def load_plugin_engine_config(plugin_info: Dict[str, Any]) -> Dict[str, Any]:
    """
    加载插件引擎配置
    
    Args:
        plugin_info: 插件信息
        
    Returns:
        引擎配置，如果插件未指定则使用默认配置
    """
    # 获取引擎类型
    engine_type = plugin_info.get("engine", "").lower()
    
    # 获取默认配置
    default_engine_config = DEFAULT_CONFIG.get("default_engine_config", {}).get(engine_type, {})
    
    # 尝试从plugin_info的config字段获取引擎配置
    engine_config = {}
    if "config" in plugin_info and isinstance(plugin_info["config"], dict):
        engine_config = plugin_info["config"].get("engine_config", {})
    
    # 如果没有配置，尝试从配置文件读取
    if not engine_config and "config_file_path" in plugin_info:
        try:
            config_file_path = plugin_info["config_file_path"]
            with open(config_file_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                if isinstance(config, dict):
                    engine_config = config.get("engine_config", {})
        except Exception:
            # 如果读取失败，使用默认配置，不需要记录警告日志
            pass
    
    # 如果插件没有指定引擎配置，直接返回默认配置
    if not engine_config:
        result_config = default_engine_config.copy()
    else:
        # 深度合并，优先级: 插件配置 > 默认配置
        result_config = default_engine_config.copy()
        _deep_update(result_config, engine_config)

    return result_config


def adapt_plugin_method(method: Callable) -> Callable:
    """
    适配插件方法装饰器，使其支持同步或异步调用
    
    Args:
        method: 原始方法
        
    Returns:
        适配后的方法
    """
    # 如果已经是异步方法，直接返回
    if asyncio.iscoroutinefunction(method):
        return method
    
    # 包装同步方法为异步方法
    async def wrapper(*args, **kwargs):
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None, 
            lambda: method(*args, **kwargs)
        )
    
    # 保留原始方法签名
    wrapper.__name__ = method.__name__
    wrapper.__doc__ = method.__doc__
    
    return wrapper


class PluginMethodProxy:
    """插件方法代理，允许同步或异步调用"""
    
    def __init__(self, plugin_instance: Any):
        """
        初始化代理
        
        Args:
            plugin_instance: 插件实例
        """
        self._plugin = plugin_instance
        self._async_methods = {}
        
        # 检查插件方法并创建适配
        for name in ['process']:
            if hasattr(self._plugin, name):
                method = getattr(self._plugin, name)
                self._async_methods[name] = adapt_plugin_method(method)
                
    async def process(self, *args, **kwargs):
        """插件处理方法代理"""
        if 'process' in self._async_methods:
            return await self._async_methods['process'](*args, **kwargs)
        raise AttributeError("插件未实现'process'方法")