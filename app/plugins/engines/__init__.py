"""
推理引擎模块
提供引擎池管理器全局单例访问
"""
import asyncio
from typing import Optional

from app.plugins.engines.pool import EnginePoolManager
from app.plugins.engines.compat import DEFAULT_CONFIG

# 全局单例
_engine_pool_instance: Optional[EnginePoolManager] = None
_engine_pool_lock = asyncio.Lock()


async def get_engine_pool() -> EnginePoolManager:
    """
    获取引擎池管理器单例
    
    Returns:
        引擎池管理器实例
    """
    global _engine_pool_instance
    
    if _engine_pool_instance is None:
        async with _engine_pool_lock:
            if _engine_pool_instance is None:
                # 使用默认配置
                _engine_pool_instance = EnginePoolManager(DEFAULT_CONFIG)
    
    return _engine_pool_instance


# 导出模块类，方便直接导入
from app.plugins.engines.base import InferenceEngine
from app.plugins.engines.onnx import ONNXRuntimeEngine
