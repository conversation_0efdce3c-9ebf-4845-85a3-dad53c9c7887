"""
推理引擎异常定义模块
包含引擎相关的所有异常类
"""
from typing import Optional


class EngineException(Exception):
    """推理引擎异常基类"""
    def __init__(self, message: str, engine_type: Optional[str] = None, code: Optional[int] = None):
        self.message = message
        self.engine_type = engine_type
        self.code = code
        super().__init__(f"[{engine_type or 'Engine'}] {message}")


class UnsupportedEngineException(EngineException):
    """不支持的引擎类型异常"""
    pass


class ModelLoadException(EngineException):
    """模型加载异常"""
    pass


class EngineInitException(EngineException):
    """引擎初始化异常"""
    pass


class EngineRuntimeException(EngineException):
    """引擎运行时异常"""
    pass


class PredictionException(EngineException):
    """预测执行异常"""
    pass


class EngineResourceException(EngineException):
    """引擎资源异常，例如内存不足、资源限制等"""
    pass


class PluginExecutionException(Exception):
    """插件执行异常，封装插件执行过程中的错误"""
    pass 