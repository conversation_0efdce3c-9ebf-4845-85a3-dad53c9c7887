"""
插件加载器模块
负责加载和实例化插件
"""
import importlib.util
import sys
import os
import yaml
from typing import Dict, Any, Type

from app.core.log import plugin_logger as logger

class PluginLoader:
    """插件加载器，负责加载插件Python模块并实例化"""
    
    @staticmethod
    def load_plugin(plugin_info: Dict[str, Any]) -> Any:
        """
        根据插件信息加载并实例化插件
        
        Args:
            plugin_info: 插件信息字典（PluginInfo）
            
        Returns:
            初始化的插件实例
            
        Raises:
            ValueError: 插件信息无效
            ImportError: 插件加载失败
        """
        python_file_path = plugin_info.get("python_file_path")
        plugin_code = plugin_info.get("plugin_code")
        plugin_version = plugin_info.get("plugin_version")

        if not os.path.exists(python_file_path):
            raise ValueError(f"插件Python文件不存在: {python_file_path}")
            
        # 加载插件配置
        plugin_config = PluginLoader._load_plugin_config(plugin_info)
        
        # 动态加载Python模块
        try:
            module_name = f"plugin_{plugin_code}_{plugin_version}"
            spec = importlib.util.spec_from_file_location(module_name, python_file_path)
            plugin_module = importlib.util.module_from_spec(spec)
            
            # 将模块添加到sys.modules，使其可以被导入
            sys.modules[module_name] = plugin_module
            spec.loader.exec_module(plugin_module)
            
            # 查找插件类
            plugin_class = PluginLoader._discover_plugin_class(plugin_module)
            
            # 实例化插件
            logger.info(f"实例化插件: {plugin_code} {plugin_version}")
            return plugin_class(plugin_config)
        
        except Exception as e:
            logger.exception(f"加载插件失败: {plugin_code} {plugin_version}, 错误: {str(e)}")
            raise ImportError(f"加载插件失败: {str(e)}")
    
    @staticmethod
    def _load_plugin_config(plugin_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        加载插件配置
        
        Args:
            plugin_info: 插件信息字典（PluginInfo）
            
        Returns:
            插件配置
        """

        config = {}
        config_file_path = plugin_info.get("config_file_path")
        
        if config_file_path:
            if os.path.exists(config_file_path):
                try:
                    with open(config_file_path, 'r', encoding='utf-8') as f:
                        config = yaml.safe_load(f)
                        if not isinstance(config, dict):
                            config = {}
                except Exception as e:
                    logger.warning(f"从配置文件加载插件配置失败: {config_file_path}, 错误: {e}")
        
        return config
    
    @staticmethod
    def _discover_plugin_class(module) -> Type:
        """
        发现模块中的插件类
        
        Args:
            module: 加载的Python模块
            
        Returns:
            插件类
            
        Raises:
            ValueError: 未找到有效的插件类
        """
        # 优先查找名为Plugin的类
        if hasattr(module, "Plugin"):
            return getattr(module, "Plugin")
        
        # 其次检查模块中所有非内置类
        # 寻找实现了必要方法的第一个类
        for attr_name in dir(module):
            # 跳过内置属性
            if attr_name.startswith("__"):
                continue
                
            attr = getattr(module, attr_name)
            if isinstance(attr, type) and attr.__module__ == module.__name__:
                # 检查是否有必要的方法
                if hasattr(attr, "process"):
                    return attr
        
        # 未找到有效的插件类
        raise ValueError("未在插件模块中找到有效的插件类") 