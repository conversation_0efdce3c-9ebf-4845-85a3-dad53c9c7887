"""
任务相关数据库模型
"""
from sqlalchemy import Column, Integer, BigInteger, String, DateTime, Boolean, func, Index
from app.core.db.base import Base
from sqlalchemy.dialects.postgresql import JSONB


class Task(Base):
    """任务表"""
    __tablename__ = "task"

    id = Column(BigInteger, primary_key=True, index=True, comment="任务ID")
    total_items = Column(Integer, nullable=False, default=0, comment="总任务项数量")
    status = Column(String(20), nullable=False, default="pending", comment="任务状态")
    priority = Column(Integer, nullable=False, default=1, comment="优先级")
    created_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        comment="创建时间",
    )
    updated_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间",
    )
    started_at = Column(DateTime(timezone=True), nullable=True, comment="开始时间")
    completed_at = Column(DateTime(timezone=True), nullable=True, comment="完成时间")

    # 索引
    __table_args__ = (
        Index("idx_task_status", status),
        Index("idx_task_priority", priority),
    )


class TaskItem(Base):
    """任务项表"""
    __tablename__ = "task_item"

    id = Column(BigInteger, primary_key=True, index=True, comment="任务项ID")
    task_id = Column(BigInteger, nullable=False, comment="任务ID")
    data_id = Column(String(100), nullable=False, comment="数据ID")
    file_type = Column(String(20), nullable=False, comment="文件类型")
    media_type = Column(String(20), nullable=False, comment="媒体类型")
    file_id = Column(BigInteger, nullable=True, comment="文件ID")
    file_url = Column(String(500), nullable=False, comment="文件URL")
    file_hash = Column(String(64), nullable=True, comment="文件哈希值")
    plugin_code = Column(String(100), nullable=False, comment="插件代码")
    plugin_version = Column(String(20), nullable=False, comment="插件版本")
    params = Column(JSONB, nullable=True, comment="处理参数")
    status = Column(String(20), nullable=False, default="pending", comment="任务项状态")
    error = Column(String(500), nullable=True, comment="错误信息")
    result = Column(JSONB, nullable=True, comment="处理结果")
    from_cache = Column(Boolean, nullable=False, default=False, comment="是否来自缓存")
    process_time = Column(JSONB, nullable=True, comment="处理各阶段时间")
    created_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        comment="创建时间",
    )
    updated_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间",
    )
    started_at = Column(DateTime(timezone=True), nullable=True, comment="开始时间")
    completed_at = Column(DateTime(timezone=True), nullable=True, comment="完成时间")

    # 索引
    __table_args__ = (
        Index("idx_task_item_task_id_status", task_id, status),
        Index("idx_task_item_file_id", file_id),
        Index("idx_task_item_plugin", plugin_code, plugin_version),
        # 添加分页查询优化索引（task_id + id 确保排序稳定性）
        Index("idx_task_item_pagination", task_id, id),
    )
