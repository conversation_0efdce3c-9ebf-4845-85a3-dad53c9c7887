"""
文件模型
"""

from sqlalchemy import (
    Boolean,
    Column,
    DateTime,
    String,
    BigInteger,
    Text,
    Index,
    func,
)
from sqlalchemy.dialects.postgresql import JSONB

from app.core.db.base import Base


class File(Base):
    """
    文件表：存储文件元数据和存储路径信息
    """
    __tablename__ = "file"

    id = Column(BigInteger, primary_key=True, comment="雪花ID，文件唯一标识")
    url = Column(Text, nullable=False, comment="文件唯一访问URL")
    size = Column(BigInteger, nullable=False, comment="文件大小(字节)")
    oss_path = Column(Text, nullable=False, comment="OSS存储路径")
    local_path = Column(Text, nullable=True, comment="本地存储路径")
    content_type = Column(String(100), nullable=False, comment="MIME类型")
    file_type = Column(String(20), nullable=False, comment="文件类型")
    media_type = Column(String(50), nullable=True, comment="媒体类型")
    file_hash = Column(String(64), nullable=False, unique=True, comment="SHA256哈希值")
    file_metadata = Column(JSONB, nullable=True, comment="文件元数据")
    platform = Column(String(50), nullable=True, comment="文件来源平台")
    is_deleted = Column(Boolean, nullable=False, default=False, comment="逻辑删除标记")
    created_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        comment="创建时间",
    )
    updated_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间",
    )

    # 索引
    __table_args__ = (
        Index("idx_file_hash", "file_hash", unique=True),
        Index("idx_file_url", "url"),
        Index("idx_file_is_deleted", "is_deleted"),
    ) 