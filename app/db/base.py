"""
SQLAlchemy ORM 基类
"""
from datetime import datetime
from typing import Any, Dict

from sqlalchemy import Column, DateTime, func
from sqlalchemy.ext.declarative import as_declarative, declared_attr


@as_declarative()
class Base:
    """SQLAlchemy ORM 模型基类"""

    id: Any
    __name__: str

    @declared_attr
    def __tablename__(cls) -> str:
        """
        自动生成表名
        使用类名的小写形式作为表名
        """
        return cls.__name__.lower()

    def to_dict(self) -> Dict[str, Any]:
        """将模型实例转换为字典"""
        return {c.name: getattr(self, c.name) for c in self.__table__.columns} 