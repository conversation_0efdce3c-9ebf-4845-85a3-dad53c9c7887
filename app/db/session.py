"""
数据库会话管理模块
"""
from contextlib import asynccontextmanager
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

from app.config.settings import settings
from app.log.sql_logger import sql_logger

# 创建异步引擎
engine = create_async_engine(
    str(settings.DATABASE_URL),
    echo=settings.DB_ECHO,
    future=True,
    pool_pre_ping=True,
    pool_size=10,
    max_overflow=20,
    connect_args={"server_settings": {"TimeZone": "UTC"}},  # 对于asyncpg使用server_settings
)

# 创建异步会话工厂
AsyncSessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine,
    class_=AsyncSession,
    expire_on_commit=False,
)


async def get_db() -> AsyncSession:
    """
    获取数据库会话依赖
    用于FastAPI依赖注入系统

    Yields:
        AsyncSession: 异步数据库会话
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


@asynccontextmanager
async def get_async_db():
    """
    获取数据库会话异步上下文管理器
    用于async with语法

    Yields:
        AsyncSession: 异步数据库会话
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise

async def init_sql_logger():
    """
    初始化SQL日志监听器
    需要在应用启动时调用
    """
    # 由于AsyncEngine没有直接的事件系统，我们需要访问同步引擎
    sync_engine = engine.sync_engine

    # 初始化SQL日志监听器
    sql_logger.initialize(sync_engine)