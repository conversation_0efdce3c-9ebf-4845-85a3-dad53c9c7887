"""
命名风格转换工具函数
"""
import re

def snake_to_camel(snake_str: str) -> str:
    """
    将蛇形命名转换为小驼峰命名
    例如：user_id -> userId
    
    Args:
        snake_str: 蛇形命名字符串
        
    Returns:
        str: 转换后的小驼峰命名字符串
    """
    components = snake_str.split('_')
    return components[0] + ''.join(x.title() for x in components[1:])


def camel_to_snake(camel_str: str) -> str:
    """
    将小驼峰命名转换为蛇形命名
    例如：userId -> user_id
    
    Args:
        camel_str: 小驼峰命名字符串
        
    Returns:
        str: 转换后的蛇形命名字符串
    """
    snake_str = re.sub(r'(?<!^)(?=[A-Z])', '_', camel_str).lower()
    return snake_str

