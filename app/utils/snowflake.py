"""
优化的单机多Worker雪花算法实现
专门针对 FastAPI 单机多Worker部署场景优化
基于进程ID确定性分配Worker ID，避免冲突
"""
import os
import time
from typing import Optional
from app.core.log import logger


class OptimizedSnowflake:
    """优化的单机多Worker雪花算法"""
    
    def __init__(self):
        # 雪花算法标准配置
        self.epoch = 1640995200000  # 2022-01-01 00:00:00 UTC
        self.worker_id_bits = 5
        self.datacenter_id_bits = 5
        self.sequence_bits = 12
        
        # 最大值计算
        self.max_worker_id = (1 << self.worker_id_bits) - 1  # 31
        self.max_datacenter_id = (1 << self.datacenter_id_bits) - 1  # 31
        self.max_sequence = (1 << self.sequence_bits) - 1  # 4095
        
        # 位移量
        self.worker_id_shift = self.sequence_bits  # 12
        self.datacenter_id_shift = self.sequence_bits + self.worker_id_bits  # 17
        self.timestamp_left_shift = (
            self.sequence_bits + self.worker_id_bits + self.datacenter_id_bits  # 22
        )
        
        # 运行时状态
        self.sequence = 0
        self.last_timestamp = -1
        
        # 核心改进：基于进程ID的确定性分配
        self.worker_id = self._get_worker_id()
        self.datacenter_id = self._get_datacenter_id()
        
        logger.info(
            f"雪花算法初始化完成: Worker ID={self.worker_id}, "
            f"Datacenter ID={self.datacenter_id}, PID={os.getpid()}"
        )
    
    def _get_worker_id(self) -> int:
        """基于进程ID确定性分配Worker ID"""
        # 优先级1: 环境变量配置
        if 'SNOWFLAKE_WORKER_ID' in os.environ:
            try:
                worker_id = int(os.environ['SNOWFLAKE_WORKER_ID'])
                if 0 <= worker_id <= self.max_worker_id:
                    logger.info(f"使用环境变量配置的 Worker ID: {worker_id}")
                    return worker_id
                else:
                    logger.warning(f"环境变量 SNOWFLAKE_WORKER_ID 超出范围: {worker_id}")
            except ValueError:
                logger.warning(f"环境变量 SNOWFLAKE_WORKER_ID 格式错误: {os.environ['SNOWFLAKE_WORKER_ID']}")
        
        # 优先级2: 基于进程ID的确定性分配（核心改进）
        pid = os.getpid()
        
        # 使用进程ID模运算，确保不同进程获得不同的Worker ID
        # 这是针对单机多Worker场景的关键优化
        worker_id = pid % (self.max_worker_id + 1)
        
        logger.info(f"基于进程ID生成 Worker ID: {worker_id} (PID: {pid})")
        return worker_id
    
    def _get_datacenter_id(self) -> int:
        """获取Datacenter ID"""
        # 优先级1: 环境变量配置
        if 'SNOWFLAKE_DATACENTER_ID' in os.environ:
            try:
                datacenter_id = int(os.environ['SNOWFLAKE_DATACENTER_ID'])
                if 0 <= datacenter_id <= self.max_datacenter_id:
                    logger.info(f"使用环境变量配置的 Datacenter ID: {datacenter_id}")
                    return datacenter_id
                else:
                    logger.warning(f"环境变量 SNOWFLAKE_DATACENTER_ID 超出范围: {datacenter_id}")
            except ValueError:
                logger.warning(f"环境变量 SNOWFLAKE_DATACENTER_ID 格式错误: {os.environ['SNOWFLAKE_DATACENTER_ID']}")
        
        # 优先级2: 单机部署固定为0
        # 在单机多Worker场景下，所有Worker使用相同的Datacenter ID
        datacenter_id = 0
        
        logger.info(f"使用默认 Datacenter ID: {datacenter_id} (单机部署)")
        return datacenter_id
    
    def generate_id(self) -> int:
        """生成雪花ID"""
        timestamp = self._current_timestamp()
        
        # 时钟回拨检测
        if timestamp < self.last_timestamp:
            raise Exception(
                f"时钟回拨检测: 当前时间戳 {timestamp} 小于上次时间戳 {self.last_timestamp}, "
                f"回拨了 {self.last_timestamp - timestamp}ms"
            )
        
        # 同一毫秒内的序列号处理
        if timestamp == self.last_timestamp:
            self.sequence = (self.sequence + 1) & self.max_sequence
            if self.sequence == 0:
                # 序列号用完，等待下一毫秒
                timestamp = self._wait_next_timestamp(self.last_timestamp)
        else:
            # 新的毫秒，重置序列号
            self.sequence = 0
        
        self.last_timestamp = timestamp
        
        # 构造64位雪花ID
        snowflake_id = (
            ((timestamp - self.epoch) << self.timestamp_left_shift) |
            (self.datacenter_id << self.datacenter_id_shift) |
            (self.worker_id << self.worker_id_shift) |
            self.sequence
        )
        
        return snowflake_id
    
    def _current_timestamp(self) -> int:
        """获取当前时间戳（毫秒）"""
        return int(time.time() * 1000)
    
    def _wait_next_timestamp(self, last_timestamp: int) -> int:
        """等待下一个时间戳"""
        timestamp = self._current_timestamp()
        while timestamp <= last_timestamp:
            timestamp = self._current_timestamp()
        return timestamp
    
    def parse_id(self, snowflake_id: int) -> dict:
        """解析雪花ID"""
        # 提取各个部分
        timestamp = ((snowflake_id >> self.timestamp_left_shift) + self.epoch)
        datacenter_id = (
            (snowflake_id >> self.datacenter_id_shift) & 
            ((1 << self.datacenter_id_bits) - 1)
        )
        worker_id = (
            (snowflake_id >> self.worker_id_shift) & 
            ((1 << self.worker_id_bits) - 1)
        )
        sequence = snowflake_id & ((1 << self.sequence_bits) - 1)
        
        return {
            'id': snowflake_id,
            'timestamp': timestamp,
            'datetime': time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(timestamp / 1000)),
            'datacenter_id': datacenter_id,
            'worker_id': worker_id,
            'sequence': sequence
        }
    
    def get_info(self) -> dict:
        """获取雪花算法配置信息"""
        return {
            'worker_id': self.worker_id,
            'datacenter_id': self.datacenter_id,
            'epoch': self.epoch,
            'worker_id_bits': self.worker_id_bits,
            'datacenter_id_bits': self.datacenter_id_bits,
            'sequence_bits': self.sequence_bits,
            'max_worker_id': self.max_worker_id,
            'max_datacenter_id': self.max_datacenter_id,
            'max_sequence': self.max_sequence,
            'current_pid': os.getpid(),
            'timestamp_left_shift': self.timestamp_left_shift,
            'datacenter_id_shift': self.datacenter_id_shift,
            'worker_id_shift': self.worker_id_shift
        }


# 全局实例 - 单例模式
_snowflake_instance: Optional[OptimizedSnowflake] = None


def get_snowflake_instance() -> OptimizedSnowflake:
    """获取雪花算法单例实例"""
    global _snowflake_instance
    if _snowflake_instance is None:
        _snowflake_instance = OptimizedSnowflake()
    return _snowflake_instance


def generate_snowflake_id() -> int:
    """生成雪花ID的便捷函数"""
    return get_snowflake_instance().generate_id()


def parse_snowflake_id(snowflake_id: int) -> dict:
    """解析雪花ID的便捷函数"""
    return get_snowflake_instance().parse_id(snowflake_id)


def get_snowflake_info() -> dict:
    """获取雪花算法配置信息的便捷函数"""
    return get_snowflake_instance().get_info()


# 兼容性函数 - 保持与原有代码的兼容性
async def init_snowflake_worker():
    """
    兼容性函数 - 新实现无需初始化
    保留此函数以保持与现有代码的兼容性
    """
    logger.info("优化雪花算法无需初始化，自动配置完成")
    # 触发实例创建，确保在应用启动时完成初始化
    instance = get_snowflake_instance()
    logger.info(f"雪花算法实例已创建: Worker ID={instance.worker_id}, PID={os.getpid()}")


def validate_multi_worker_setup():
    """
    验证多Worker设置的辅助函数
    用于检查不同Worker是否获得了不同的Worker ID
    """
    instance = get_snowflake_instance()
    info = instance.get_info()
    
    logger.info("=== 多Worker设置验证 ===")
    logger.info(f"当前进程 PID: {info['current_pid']}")
    logger.info(f"分配的 Worker ID: {info['worker_id']}")
    logger.info(f"Datacenter ID: {info['datacenter_id']}")
    logger.info(f"Worker ID 计算方式: {info['current_pid']} % 32 = {info['worker_id']}")
    logger.info("========================")
    
    return info


# 导出主要函数
__all__ = [
    'OptimizedSnowflake',
    'generate_snowflake_id',
    'parse_snowflake_id',
    'get_snowflake_info',
    'init_snowflake_worker',  # 兼容性函数
    'validate_multi_worker_setup',  # 验证函数
]
