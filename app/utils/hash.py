"""
哈希工具函数
"""
import hashlib
from typing import Any, Dict, Optional, List, Union
import canonicaljson


def _normalize_value(value: Any) -> Any:
    """
    规范化值，处理特殊类型
    
    Args:
        value: 任意类型的值
        
    Returns:
        规范化后的值
    """
    if value is None:
        return None
    
    # 处理浮点数精度
    if isinstance(value, float):
        # 使用固定精度格式化浮点数，然后转回浮点数
        return float(f"{value:.6f}")
    
    # 处理字典
    if isinstance(value, dict):
        return {k: _normalize_value(v) for k, v in value.items()}
    
    # 处理列表或元组
    if isinstance(value, (list, tuple)):
        return [_normalize_value(item) for item in value]
    
    return value


def calculate_params_hash(params: Optional[Dict[str, Any]]) -> str:
    """
    计算参数的哈希值
    
    Args:
        params: 参数字典，可以为None
        
    Returns:
        str: 参数的SHA-256哈希值(十六进制字符串)
    """
    if params is None:
        # 如果参数为None，返回特殊标记的哈希值
        return hashlib.sha256(b'__NULL__').hexdigest()
    elif not params:
        # 如果参数是空字典，返回空字典的哈希值
        return hashlib.sha256(b'{}').hexdigest()
    
    # 对参数进行规范化处理
    normalized_params = _normalize_value(params)
    
    # 使用canonicaljson库进行标准化序列化
    normalized_json = canonicaljson.encode_canonical_json(normalized_params)
    
    # 计算SHA-256哈希值
    return hashlib.sha256(normalized_json).hexdigest() 