"""
OpenTelemetry trace context传播工具
为异步任务提供trace context传播和span管理功能
"""
import asyncio
import contextvars
import json
from typing import Callable, Any, Optional, Dict, Union
from functools import wraps

from opentelemetry import trace, context
from opentelemetry.trace import Status, StatusCode, SpanKind
from opentelemetry.propagate import inject, extract

# 避免循环导入，直接使用print进行错误输出
import warnings


# ============================================================================
# Trace ID 和 Span ID 获取函数（从 app.log.trace_id 迁移）
# ============================================================================

def get_trace_id() -> Optional[str]:
    """
    从OpenTelemetry context获取当前的trace_id

    Returns:
        当前的trace_id（32位hex格式），如果没有活跃span则返回None
    """
    current_span = trace.get_current_span()
    if current_span and current_span.is_recording():
        span_context = current_span.get_span_context()
        return f"{span_context.trace_id:032x}"
    return None


def get_span_id() -> Optional[str]:
    """
    从OpenTelemetry context获取当前的span_id

    Returns:
        当前的span_id（16位hex格式），如果没有活跃span则返回None
    """
    current_span = trace.get_current_span()
    if current_span and current_span.is_recording():
        span_context = current_span.get_span_context()
        return f"{span_context.span_id:016x}"
    return None


def get_trace_info() -> Dict[str, Any]:
    """
    获取完整的trace信息

    Returns:
        包含trace_id、span_id等信息的字典
    """
    current_span = trace.get_current_span()
    if current_span and current_span.is_recording():
        span_context = current_span.get_span_context()
        return {
            "trace_id": f"{span_context.trace_id:032x}",
            "span_id": f"{span_context.span_id:016x}",
            "trace_flags": span_context.trace_flags,
            "span_name": getattr(current_span, '_name', None),
            "is_recording": True
        }
    return {
        "trace_id": None,
        "span_id": None,
        "trace_flags": None,
        "span_name": None,
        "is_recording": False
    }


# ============================================================================
# Trace Context 传播和管理函数
# ============================================================================

def copy_trace_context() -> context.Context:
    """
    复制当前的OpenTelemetry trace context用于异步任务传播

    Returns:
        context.Context: 当前的trace context
    """
    try:
        return context.get_current()
    except Exception as e:
        warnings.warn(f"复制trace context失败: {e}")
        # 返回空context，不影响业务逻辑
        return context.Context()


def serialize_trace_context(trace_context: Optional[context.Context] = None) -> str:
    """
    将trace context序列化为字符串，用于存储到Redis

    Args:
        trace_context: 要序列化的trace context，如果为None则使用当前context

    Returns:
        str: 序列化后的trace context字符串
    """
    try:
        if trace_context is None:
            trace_context = context.get_current()

        # 使用字典存储trace headers
        carrier = {}
        inject(carrier, context=trace_context)

        # 序列化为JSON字符串
        return json.dumps(carrier)
    except Exception as e:
        warnings.warn(f"序列化trace context失败: {e}")
        return "{}"


def deserialize_trace_context(serialized_context: str) -> context.Context:
    """
    从字符串反序列化trace context

    Args:
        serialized_context: 序列化的trace context字符串

    Returns:
        context.Context: 反序列化后的trace context
    """
    try:
        if not serialized_context:
            return context.Context()

        # 反序列化JSON
        carrier = json.loads(serialized_context)

        # 提取trace context
        return extract(carrier)
    except Exception as e:
        warnings.warn(f"反序列化trace context失败: {e}")
        return context.Context()


def run_with_trace_context(coro, trace_context: context.Context):
    """
    在指定的trace context中运行协程
    
    Args:
        coro: 要运行的协程
        trace_context: 要使用的trace context
        
    Returns:
        协程对象，可以传递给asyncio.create_task
    """
    async def wrapper():
        token = context.attach(trace_context)
        try:
            return await coro
        finally:
            context.detach(token)
    
    return wrapper()


def create_child_span(
    name: str,
    kind: SpanKind = SpanKind.INTERNAL,
    attributes: Optional[Dict[str, Any]] = None
):
    """
    创建子span的上下文管理器
    
    Args:
        name: span名称
        kind: span类型
        attributes: span属性
        
    Returns:
        span上下文管理器
    """
    try:
        tracer = trace.get_tracer(__name__)
        span = tracer.start_as_current_span(
            name,
            kind=kind,
            attributes=attributes or {}
        )
        return span
    except Exception as e:
        warnings.warn(f"创建span失败: {e}")
        # 返回无操作span，不影响业务逻辑
        return trace.NonRecordingSpan(trace.INVALID_SPAN_CONTEXT)


def traced_async_task(
    span_name: str,
    kind: SpanKind = SpanKind.INTERNAL,
    attributes: Optional[Dict[str, Any]] = None,
    record_exception: bool = True
):
    """
    异步任务的trace装饰器，自动为异步函数创建span
    
    Args:
        span_name: span名称
        kind: span类型
        attributes: 静态span属性
        record_exception: 是否记录异常到span
        
    Returns:
        装饰器函数
    """
    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 动态生成span名称，支持参数插值
            dynamic_span_name = span_name
            try:
                # 如果span_name包含格式化占位符，尝试格式化
                if "{" in span_name and "}" in span_name:
                    # 从函数参数中提取可能的格式化参数
                    format_kwargs = {}
                    if args:
                        # 常见的第一个参数模式
                        if hasattr(args[0], 'id'):
                            format_kwargs['id'] = args[0].id
                        if len(args) > 1 and isinstance(args[1], (int, str)):
                            format_kwargs['task_id'] = args[1]
                    
                    # 从kwargs中提取
                    for key in ['task_id', 'item_id', 'plugin_code', 'id']:
                        if key in kwargs:
                            format_kwargs[key] = kwargs[key]
                    
                    if format_kwargs:
                        dynamic_span_name = span_name.format(**format_kwargs)
            except Exception:
                # 格式化失败时使用原始名称
                pass
            
            # 创建span
            with create_child_span(
                dynamic_span_name,
                kind=kind,
                attributes=attributes
            ) as span:
                try:
                    # 添加函数相关属性
                    if span.is_recording():
                        span.set_attribute("function.name", func.__name__)
                        span.set_attribute("function.module", func.__module__)
                        
                        # 添加关键参数到span属性
                        if args and hasattr(args[0], 'id'):
                            span.set_attribute("entity.id", str(args[0].id))
                        
                        for key in ['task_id', 'item_id', 'plugin_code']:
                            if key in kwargs:
                                span.set_attribute(f"param.{key}", str(kwargs[key]))
                    
                    # 执行函数
                    result = await func(*args, **kwargs)
                    
                    # 设置成功状态
                    if span.is_recording():
                        span.set_status(Status(StatusCode.OK))
                    
                    return result
                    
                except Exception as e:
                    # 处理异常
                    if span.is_recording():
                        span.set_status(Status(StatusCode.ERROR, str(e)))
                        if record_exception:
                            span.record_exception(e)
                    
                    warnings.warn(f"执行 {func.__name__} 时发生异常: {e}")
                    raise
        
        return wrapper
    return decorator


def add_span_attributes(**attributes):
    """
    为当前span添加属性的便捷函数
    
    Args:
        **attributes: 要添加的属性键值对
    """
    try:
        current_span = trace.get_current_span()
        if current_span and current_span.is_recording():
            for key, value in attributes.items():
                current_span.set_attribute(key, str(value))
    except Exception as e:
        pass  # 静默忽略错误，不影响业务逻辑


def add_span_event(name: str, attributes: Optional[Dict[str, Any]] = None):
    """
    为当前span添加事件的便捷函数
    
    Args:
        name: 事件名称
        attributes: 事件属性
    """
    try:
        current_span = trace.get_current_span()
        if current_span and current_span.is_recording():
            current_span.add_event(name, attributes or {})
    except Exception as e:
        pass  # 静默忽略错误，不影响业务逻辑


def create_task_with_context(coro, name: Optional[str] = None) -> asyncio.Task:
    """
    创建带有当前trace context的异步任务
    
    Args:
        coro: 协程对象
        name: 任务名称
        
    Returns:
        asyncio.Task: 创建的任务
    """
    try:
        # 复制当前context
        current_context = copy_trace_context()
        
        # 在context中运行协程
        wrapped_coro = run_with_trace_context(coro, current_context)
        
        # 创建任务
        if name:
            return asyncio.create_task(wrapped_coro, name=name)
        else:
            return asyncio.create_task(wrapped_coro)
            
    except Exception as e:
        warnings.warn(f"创建带context的任务失败: {e}")
        # 降级到普通任务创建
        if name:
            return asyncio.create_task(coro, name=name)
        else:
            return asyncio.create_task(coro)


# 便捷的全局函数别名
trace_async = traced_async_task
with_span = create_child_span
