"""
版本号比较工具
支持语义化版本(SemVer)比较
"""
import re
from typing import Tuple, List, Optional, Union


class Version:
    """语义化版本类"""
    
    def __init__(self, version_str: str):
        """
        初始化版本对象
        
        Args:
            version_str: 版本号字符串，如"1.0.0"，"2.3.4-beta.1"
        """
        self.version_str = version_str
        self.major, self.minor, self.patch, self.prerelease, self.build = self._parse_version(version_str)
    
    def _parse_version(self, version_str: str) -> Tuple[int, int, int, Optional[str], Optional[str]]:
        """
        解析版本号字符串
        
        Args:
            version_str: 版本号字符串
            
        Returns:
            Tuple[int, int, int, Optional[str], Optional[str]]: 主版本号、次版本号、修订号、预发布标识和构建标识
            
        Raises:
            ValueError: 版本号格式错误
        """
        # 基本格式：major.minor.patch[-prerelease][+build]
        pattern = r'^(\d+)\.(\d+)\.(\d+)(?:-([0-9A-Za-z.-]+))?(?:\+([0-9A-Za-z.-]+))?$'
        match = re.match(pattern, version_str)
        
        # 尝试匹配 major.minor 格式
        if not match:
            pattern = r'^(\d+)\.(\d+)(?:-([0-9A-Za-z.-]+))?(?:\+([0-9A-Za-z.-]+))?$'
            match = re.match(pattern, version_str)
            if match:
                major, minor = map(int, match.groups()[:2])
                prerelease = match.group(3)
                build = match.group(4)
                return major, minor, 0, prerelease, build
        
        # 尝试匹配简单的 major 格式
        if not match:
            pattern = r'^(\d+)(?:-([0-9A-Za-z.-]+))?(?:\+([0-9A-Za-z.-]+))?$'
            match = re.match(pattern, version_str)
            if match:
                major = int(match.group(1))
                prerelease = match.group(2)
                build = match.group(3)
                return major, 0, 0, prerelease, build
                
        # 如果都不匹配，抛出异常
        if not match:
            raise ValueError(f"无效的版本号格式: {version_str}")
        
        major, minor, patch = map(int, match.groups()[:3])
        prerelease = match.group(4)
        build = match.group(5)
        
        return major, minor, patch, prerelease, build
    
    def _compare_prerelease(self, other_prerelease: Optional[str]) -> int:
        """
        比较预发布标识
        
        Args:
            other_prerelease: 要比较的预发布标识
            
        Returns:
            int: 如果当前版本大于其他版本返回1，等于返回0，小于返回-1
        """
        # 没有预发布标识的版本高于有预发布标识的版本
        if self.prerelease is None and other_prerelease is not None:
            return 1
        if self.prerelease is not None and other_prerelease is None:
            return -1
        if self.prerelease is None and other_prerelease is None:
            return 0
        
        # 按标识符分割并比较
        self_identifiers = self.prerelease.split('.')
        other_identifiers = other_prerelease.split('.')
        
        for i in range(min(len(self_identifiers), len(other_identifiers))):
            self_id = self_identifiers[i]
            other_id = other_identifiers[i]
            
            # 数字标识符比较
            self_is_num = self_id.isdigit()
            other_is_num = other_id.isdigit()
            
            if self_is_num and other_is_num:
                self_num = int(self_id)
                other_num = int(other_id)
                if self_num != other_num:
                    return 1 if self_num > other_num else -1
            elif self_is_num:
                # 数字标识符小于非数字标识符
                return -1
            elif other_is_num:
                # 非数字标识符大于数字标识符
                return 1
            else:
                # 字符串比较
                if self_id != other_id:
                    return 1 if self_id > other_id else -1
        
        # 标识符数量较多的版本较大
        return 1 if len(self_identifiers) > len(other_identifiers) else \
              -1 if len(self_identifiers) < len(other_identifiers) else 0
    
    def __eq__(self, other: 'Version') -> bool:
        """
        比较两个版本是否相等
        
        Args:
            other: 要比较的版本
            
        Returns:
            bool: 如果两个版本相等返回True，否则返回False
        """
        if not isinstance(other, Version):
            return NotImplemented
        
        # 主要版本号、次要版本号和修订号必须相等
        if (self.major, self.minor, self.patch) != (other.major, other.minor, other.patch):
            return False
        
        # 比较预发布标识
        return self._compare_prerelease(other.prerelease) == 0
    
    def __lt__(self, other: 'Version') -> bool:
        """
        比较当前版本是否小于其他版本
        
        Args:
            other: 要比较的版本
            
        Returns:
            bool: 如果当前版本小于其他版本返回True，否则返回False
        """
        if not isinstance(other, Version):
            return NotImplemented
        
        # 比较主要版本号、次要版本号和修订号
        if self.major != other.major:
            return self.major < other.major
        if self.minor != other.minor:
            return self.minor < other.minor
        if self.patch != other.patch:
            return self.patch < other.patch
        
        # 比较预发布标识
        return self._compare_prerelease(other.prerelease) < 0
    
    def __gt__(self, other: 'Version') -> bool:
        """
        比较当前版本是否大于其他版本
        
        Args:
            other: 要比较的版本
            
        Returns:
            bool: 如果当前版本大于其他版本返回True，否则返回False
        """
        if not isinstance(other, Version):
            return NotImplemented
        
        # 比较主要版本号、次要版本号和修订号
        if self.major != other.major:
            return self.major > other.major
        if self.minor != other.minor:
            return self.minor > other.minor
        if self.patch != other.patch:
            return self.patch > other.patch
        
        # 比较预发布标识
        return self._compare_prerelease(other.prerelease) > 0
    
    def __le__(self, other: 'Version') -> bool:
        """
        比较当前版本是否小于等于其他版本
        
        Args:
            other: 要比较的版本
            
        Returns:
            bool: 如果当前版本小于等于其他版本返回True，否则返回False
        """
        return self < other or self == other
    
    def __ge__(self, other: 'Version') -> bool:
        """
        比较当前版本是否大于等于其他版本
        
        Args:
            other: 要比较的版本
            
        Returns:
            bool: 如果当前版本大于等于其他版本返回True，否则返回False
        """
        return self > other or self == other
    
    def __str__(self) -> str:
        """
        返回版本号字符串表示
        
        Returns:
            str: 版本号字符串
        """
        return self.version_str


def compare_versions(version1: str, version2: str) -> int:
    """
    比较两个版本号的大小
    
    Args:
        version1: 第一个版本号
        version2: 第二个版本号
        
    Returns:
        int: 如果version1大于version2返回1，等于返回0，小于返回-1
        
    Raises:
        ValueError: 版本号格式错误
    """
    v1 = Version(version1)
    v2 = Version(version2)
    
    if v1 > v2:
        return 1
    elif v1 < v2:
        return -1
    else:
        return 0


def is_newer_version(new_version: str, old_version: str) -> bool:
    """
    判断新版本是否比旧版本更新
    
    Args:
        new_version: 新版本号
        old_version: 旧版本号
        
    Returns:
        bool: 如果新版本更新返回True，否则返回False
        
    Raises:
        ValueError: 版本号格式错误
    """
    return compare_versions(new_version, old_version) > 0 