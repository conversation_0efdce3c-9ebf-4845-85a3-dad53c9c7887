"""
阿里云 OSS 工具类
"""
from datetime import datetime, timedelta
from typing import Dict, Tuple, Optional, Union, BinaryIO
import json
from pathlib import Path

import oss2
from oss2.models import PutObjectResult
from aliyunsdkcore.client import AcsClient
from aliyunsdksts.request.v20150401.AssumeRoleRequest import AssumeRoleRequest

from app.config.settings import settings
from app.core.log import file_logger as logger


class OSSClient:
    """阿里云 OSS 客户端"""

    def __init__(self):
        """初始化 OSS 客户端"""
        self.access_key_id = settings.OSS_ACCESS_KEY_ID
        self.access_key_secret = settings.OSS_ACCESS_KEY_SECRET
        self.endpoint = settings.OSS_ENDPOINT
        self.bucket_name = settings.OSS_BUCKET_NAME
        self.role_arn = settings.OSS_ROLE_ARN
        self.region = settings.OSS_REGION

        # 创建 ACS 客户端
        self.acs_client = AcsClient(
            ak=self.access_key_id,
            secret=self.access_key_secret,
            region_id=self.region
        )

        # 创建 OSS 认证对象
        self.auth = oss2.Auth(self.access_key_id, self.access_key_secret)
        # 创建 Bucket 对象
        self.bucket = oss2.Bucket(self.auth, self.endpoint, self.bucket_name)

    async def generate_sts_token(
        self,
        duration_seconds: int = 3600,
        session_name: Optional[str] = None
    ) -> Tuple[Dict, datetime]:
        """
        生成 STS 临时访问令牌

        Args:
            duration_seconds: 令牌有效期（秒），默认1小时
            session_name: 会话名称，默认使用时间戳

        Returns:
            Tuple[Dict, datetime]: (STS令牌信息, 过期时间)

        Raises:
            Exception: 生成令牌失败时抛出
        """
        try:
            # 创建 STS 请求
            request = AssumeRoleRequest()
            request.set_accept_format('json')
            request.set_RoleArn(self.role_arn)
            request.set_RoleSessionName(session_name or f"sts-{int(datetime.now().timestamp())}")
            request.set_DurationSeconds(duration_seconds)

            # 发送请求
            response = self.acs_client.do_action_with_exception(request)
            # 解析响应（bytes转json）
            response_dict = json.loads(response.decode('utf-8'))
            
            # 获取凭证信息
            credentials = response_dict.get('Credentials', {})

            # 解析响应
            sts_token = {
                'AccessKeyId': credentials.get('AccessKeyId'),
                'AccessKeySecret': credentials.get('AccessKeySecret'),
                'SecurityToken': credentials.get('SecurityToken'),
                'Expiration': credentials.get('Expiration')
            }

            # 计算过期时间
            expiration = datetime.strptime(
                credentials.get('Expiration'),
                "%Y-%m-%dT%H:%M:%SZ"
            )

            return sts_token, expiration

        except Exception as e:
            logger.error(f"生成 STS 令牌失败: {str(e)}")
            raise

    async def generate_presigned_url(
        self,
        object_key: str,
        expires_in: int = 3600,
        sts_token: Optional[Dict] = None
    ) -> Tuple[str, datetime]:
        """
        生成文件预签名 URL

        Args:
            object_key: 对象键（文件路径）
            expires_in: URL有效期（秒），默认1小时
            sts_token: STS令牌信息，如果提供则使用临时凭证生成URL

        Returns:
            Tuple[str, datetime]: (预签名URL, 过期时间)

        Raises:
            Exception: 生成URL失败时抛出
        """
        try:
            # 创建认证对象
            if sts_token:
                auth = oss2.StsAuth(
                    access_key_id=sts_token['AccessKeyId'],
                    access_key_secret=sts_token['AccessKeySecret'],
                    security_token=sts_token['SecurityToken']
                )
            else:
                auth = oss2.Auth(self.access_key_id, self.access_key_secret)

            # 创建 Bucket 对象
            bucket = oss2.Bucket(auth, self.endpoint, self.bucket_name)

            # 生成预签名 URL
            url = bucket.sign_url('GET', object_key, expires_in)
            
            # 确保URL使用HTTPS
            if url.startswith('http://'):
                url = url.replace('http://', 'https://')
                
            expires_at = datetime.now() + timedelta(seconds=expires_in)

            return url, expires_at

        except Exception as e:
            logger.error(f"生成预签名URL失败: {str(e)}")
            raise

    async def upload_file(
        self,
        object_key: str,
        file_path: Union[str, Path],
        headers: Optional[Dict] = None
    ) -> PutObjectResult:
        """
        上传本地文件到 OSS

        Args:
            object_key: OSS对象键（文件路径）
            file_path: 本地文件路径
            headers: 可选的HTTP头信息

        Returns:
            PutObjectResult: 上传结果

        Raises:
            Exception: 上传失败时抛出
        """
        try:
            # 确保文件存在
            file_path = Path(file_path)
            if not file_path.exists():
                raise FileNotFoundError(f"文件不存在: {file_path}")

            # 上传文件
            with open(file_path, 'rb') as f:
                result = self.bucket.put_object(
                    key=object_key,
                    data=f,
                    headers=headers
                )

            logger.info(f"文件上传成功: {object_key}")
            return result

        except Exception as e:
            logger.error(f"文件上传失败: {str(e)}")
            raise

    async def upload_stream(
        self,
        object_key: str,
        data: Union[bytes, BinaryIO, str],
        headers: Optional[Dict] = None
    ) -> PutObjectResult:
        """
        上传数据流到 OSS

        Args:
            object_key: OSS对象键（文件路径）
            data: 要上传的数据，可以是字节、文件对象或字符串
            headers: 可选的HTTP头信息

        Returns:
            PutObjectResult: 上传结果

        Raises:
            Exception: 上传失败时抛出
        """
        try:
            # 上传数据
            result = self.bucket.put_object(
                key=object_key,
                data=data,
                headers=headers
            )

            logger.info(f"数据上传成功: {object_key}")
            return result

        except Exception as e:
            logger.error(f"数据上传失败: {str(e)}")
            raise

    async def upload_file_with_progress(
        self,
        object_key: str,
        file_path: Union[str, Path],
        headers: Optional[Dict] = None,
        progress_callback: Optional[callable] = None
    ) -> PutObjectResult:
        """
        上传本地文件到 OSS，支持进度回调

        Args:
            object_key: OSS对象键（文件路径）
            file_path: 本地文件路径
            headers: 可选的HTTP头信息
            progress_callback: 进度回调函数，接收已上传的字节数和总字节数

        Returns:
            PutObjectResult: 上传结果

        Raises:
            Exception: 上传失败时抛出
        """
        try:
            # 确保文件存在
            file_path = Path(file_path)
            if not file_path.exists():
                raise FileNotFoundError(f"文件不存在: {file_path}")

            # 获取文件大小
            total_size = file_path.stat().st_size

            def default_progress_callback(consumed_bytes, total_bytes):
                """默认进度回调"""
                if total_bytes:
                    rate = int(100 * (float(consumed_bytes) / float(total_bytes)))
                    logger.debug(f"上传进度: {rate}%")

            # 使用提供的回调或默认回调
            callback = progress_callback or default_progress_callback

            # 上传文件
            with open(file_path, 'rb') as f:
                result = self.bucket.put_object(
                    key=object_key,
                    data=f,
                    headers=headers,
                    progress_callback=callback
                )

            logger.info(f"文件上传成功: {object_key}")
            return result

        except Exception as e:
            logger.error(f"文件上传失败: {str(e)}")
            raise


# 创建全局 OSS 客户端实例
oss_client = OSSClient() 