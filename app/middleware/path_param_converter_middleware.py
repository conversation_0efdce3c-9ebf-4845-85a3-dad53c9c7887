"""
路径参数命名风格转换中间件
自动将URL路径中的小驼峰命名参数转换为蛇形命名
"""
import re
from typing import Optional, List, Pattern

from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from starlette.responses import Response
from starlette.types import ASGIApp

from app.log import logger
from app.utils.naming import camel_to_snake

class PathParamConverterMiddleware(BaseHTTPMiddleware):
    """
    路径参数命名风格转换中间件
    将URL路径中的小驼峰参数名转换为蛇形参数名
    例如：/api/v1/plugins/{pluginId} -> /api/v1/plugins/{plugin_id}
    """
    
    def __init__(
        self, 
        app: ASGIApp, 
        exclude_paths: Optional[List[str]] = None,
        param_patterns: Optional[List[Pattern]] = None
    ):
        """
        初始化中间件
        
        Args:
            app: ASGI应用
            exclude_paths: 排除的路径，支持正则表达式
            param_patterns: 自定义的参数匹配模式
        """
        super().__init__(app)
        self.exclude_paths = exclude_paths or []
        # 默认匹配路径中的 {paramName} 格式，只匹配小驼峰
        self.param_patterns = param_patterns or [
            # 匹配路径中的 {paramName} 格式参数
            re.compile(r'\{([a-z][a-z0-9]*)([A-Z][a-z0-9]*)+\}')
        ]
    
    async def dispatch(
        self, request: Request, call_next: RequestResponseEndpoint
    ) -> Response:
        """
        处理请求
        
        Args:
            request: HTTP请求
            call_next: 下一个中间件或路由处理函数
            
        Returns:
            Response: HTTP响应
        """
        path = request.url.path
        
        # 检查是否在排除路径中
        for exclude in self.exclude_paths:
            if re.match(exclude, path):
                return await call_next(request)
        
        # 转换路径中的小驼峰参数名
        modified_path = path
        
        # 首先使用正则表达式匹配小驼峰参数
        for pattern in self.param_patterns:
            matches = pattern.findall(path)
            if not matches:
                continue
                
            for match in matches:
                # 组合匹配结果，获取完整的参数名
                if isinstance(match, tuple):
                    camel_param = ''.join(match)
                else:
                    camel_param = match
                    
                # 将小驼峰转换为蛇形
                snake_param = camel_to_snake(camel_param)
                if snake_param != camel_param:
                    modified_path = modified_path.replace(
                        f"{{{camel_param}}}", f"{{{snake_param}}}"
                    )
        
        # 如果路径没有变化，尝试手动检查包含大写字母的参数
        if modified_path == path:
            # 查找路径中的所有 {param} 参数
            params = re.findall(r'\{([^}]+)\}', path)
            for param in params:
                # 检查是否包含大写字母（可能是小驼峰）
                if any(c.isupper() for c in param):
                    snake_param = camel_to_snake(param)
                    if snake_param != param:
                        modified_path = modified_path.replace(
                            f"{{{param}}}", f"{{{snake_param}}}"
                        )
        
        if modified_path != path:
            # 如果路径被修改，创建新的请求
            logger.debug(f"URL路径参数转换: {path} -> {modified_path}")
            
            # scope是不可变的，需要创建一个新的scope
            scope = request.scope.copy()
            scope["path"] = modified_path
            root_path = request.scope.get("root_path", "")
            path_with_root = root_path + modified_path
            scope["path_with_root"] = path_with_root
            
            # 创建新的请求
            modified_request = Request(scope)
            
            # 复制请求参数
            modified_request._stream = request._stream
            modified_request._receive = request._receive
            
            # 使用修改后的请求继续处理
            return await call_next(modified_request)
        
        # 如果路径没有被修改，直接传递请求
        return await call_next(request) 