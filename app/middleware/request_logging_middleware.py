"""
请求日志中间件
记录API请求参数，支持简化上传请求的参数打印，并统计请求耗时
"""
import json
import time
from typing import Dict, Any, List, Set
from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from starlette.responses import Response
from starlette.types import ASGIApp
from starlette.concurrency import iterate_in_threadpool

from app.core.log import logger


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """
    请求日志中间件
    记录API请求参数，支持简化上传请求的参数打印，并统计请求耗时
    """
    
    def __init__(
        self, 
        app: ASGIApp,
        exclude_paths: List[str] = None,
        max_body_log_size: int = 1000,
        sensitive_fields: Set[str] = None,
        log_headers: bool = False,
    ):
        """
        初始化中间件
        
        Args:
            app: ASGI应用
            exclude_paths: 排除的路径，不记录日志
            max_body_log_size: 请求体日志最大长度，超过则截断
            sensitive_fields: 敏感字段列表，将被遮蔽
            log_headers: 是否记录请求头
        """
        super().__init__(app)
        self.exclude_paths = exclude_paths or ["/docs", "/redoc", "/openapi.json", "/metrics", "/health"]
        self.max_body_log_size = max_body_log_size
        self.sensitive_fields = sensitive_fields or {"password", "token", "secret", "key", "auth"}
        self.log_headers = log_headers
        
    async def dispatch(
        self, request: Request, call_next: RequestResponseEndpoint
    ) -> Response:
        """
        处理请求
        
        Args:
            request: HTTP请求
            call_next: 下一个中间件或路由处理函数
            
        Returns:
            Response: HTTP响应
        """
        # 记录请求开始时间
        start_time = time.time()
        
        # 检查是否需要记录日志
        if self._should_skip_logging(request.url.path):
            response = await call_next(request)
            return response
            
        # 记录请求信息
        method = request.method
        path = request.url.path
        query_params = dict(request.query_params)
        
        log_data = {
            "method": method,
            "path": path,
        }
        
        # 记录查询参数
        if query_params:
            log_data["query_params"] = self._mask_sensitive_data(query_params)
            
        # 记录请求头
        if self.log_headers:
            headers = dict(request.headers)
            log_data["headers"] = self._mask_sensitive_data(headers)
        
        # 处理请求体
        is_upload = self._is_upload_request(request)
        
        if not is_upload:
            # 普通请求，记录完整参数
            try:
                body = await self._get_request_body(request)
                if body:
                    log_data["body"] = self._mask_sensitive_data(body)
            except Exception as e:
                log_data["body_error"] = f"无法读取请求体: {str(e)}"
        else:
            # 上传请求，简化参数打印
            log_data["body"] = "<上传请求，参数已简化>"
            # 尝试获取上传文件的基本信息
            try:
                form = await request.form()
                files_info = []
                for key, value in form.items():
                    if hasattr(value, "filename"):
                        # 这是一个文件
                        files_info.append({
                            "field_name": key,
                            "filename": value.filename,
                            "content_type": value.content_type,
                            "size": len(await value.read()) if hasattr(value, "read") else "未知"
                        })
                        # 重置文件指针，确保后续处理不受影响
                        if hasattr(value, "seek"):
                            await value.seek(0)
                    else:
                        # 这是一个普通表单字段
                        if key.lower() not in self.sensitive_fields:
                            log_data[f"form_{key}"] = str(value)
                        else:
                            log_data[f"form_{key}"] = "******"
                
                if files_info:
                    log_data["files"] = files_info
            except Exception as e:
                log_data["form_error"] = f"无法读取表单数据: {str(e)}"
        
        # 记录请求日志
        if is_upload:
            logger.info(f"API请求开始: 方法={method}, 路径={path}, 上传请求")
        elif log_data.get("body"):
            # 将body转换为JSON格式字符串
            body_str = self._safe_json_dumps(log_data.get("body"))
            logger.info(f"API请求开始: 方法={method}, 路径={path}, 参数={body_str}")
        else:
            logger.info(f"API请求开始: 方法={method}, 路径={path}")
        
        # 处理请求
        try:
            # 获取原始响应
            response = await call_next(request)
            
            # 计算请求耗时
            process_time = time.time() - start_time
            process_time_ms = round(process_time * 1000, 2)  # 转换为毫秒
            
            # 记录响应状态码和耗时
            status_code = response.status_code
            
            # 尝试获取响应内容
            response_body = ""
            content_type = response.headers.get("content-type", "")
            
            # 通过Content-Type判断是否为JSON响应
            if "application/json" in content_type:
                try:
                    # 使用starlette的iterate_in_threadpool获取响应体
                    res_body = [section async for section in response.body_iterator]
                    # 重新设置响应体迭代器，以便能正常返回响应
                    response.body_iterator = iterate_in_threadpool(iter(res_body))
                    
                    # 解析响应体内容
                    if res_body:
                        response_body = res_body[0].decode('utf-8')
                except Exception as e:
                    response_body = f"<无法解析响应体: {str(e)}>"
            else:
                # 对于非JSON响应，不打印内容
                response_body = "<非JSON数据，不打印内容>"
            
            # 根据状态码选择日志级别
            log_message = f"API请求结束: 方法={method}, 路径={path}, 状态码={status_code}, 耗时={process_time_ms}ms"
            if response_body and response_body != "<非JSON数据，不打印内容>":
                # 将响应体转换为JSON格式字符串（如果还不是的话）
                try:
                    # 尝试解析并重新格式化为紧凑JSON
                    response_data = json.loads(response_body)
                    response_str = self._safe_json_dumps(response_data)
                except json.JSONDecodeError:
                    # 如果不是有效JSON，直接使用原字符串
                    response_str = response_body
                log_message += f", 响应={response_str}"

            if status_code >= 500:
                logger.error(log_message)
            elif status_code >= 400:
                logger.warning(log_message)
            else:
                logger.info(log_message)
                
            # 添加处理时间到响应头
            response.headers["X-Process-Time"] = str(process_time)
            
            return response
        except Exception as e:
            # 计算请求耗时
            process_time = time.time() - start_time
            process_time_ms = round(process_time * 1000, 2)  # 转换为毫秒

            # 记录异常信息
            logger.error(f"API请求异常: 方法={method}, 路径={path}, 错误={str(e)}, 耗时={process_time_ms}ms")
            raise  # 重新抛出异常，让后续的异常处理器处理
    
    def _should_skip_logging(self, path: str) -> bool:
        """
        检查是否应该跳过日志记录
        
        Args:
            path: 请求路径
            
        Returns:
            bool: 是否跳过
        """
        for exclude_path in self.exclude_paths:
            if path.startswith(exclude_path):
                return True
        return False
    
    def _is_upload_request(self, request: Request) -> bool:
        """
        检查是否是上传请求
        
        Args:
            request: HTTP请求
            
        Returns:
            bool: 是否是上传请求
        """
        content_type = request.headers.get("content-type", "")
        return "multipart/form-data" in content_type
    
    async def _get_request_body(self, request: Request) -> Dict[str, Any]:
        """
        获取请求体
        
        Args:
            request: HTTP请求
            
        Returns:
            Dict[str, Any]: 请求体数据
        """
        body = {}
        
        # 检查内容类型
        content_type = request.headers.get("content-type", "")
        
        if "application/json" in content_type:
            try:
                raw_body = await request.body()
                if raw_body:
                    body = json.loads(raw_body)
                    # 确保请求体可以再次被读取
                    await request.body()
            except json.JSONDecodeError:
                body = {"error": "Invalid JSON"}
            except Exception as e:
                body = {"error": f"Error reading body: {str(e)}"}
        elif "application/x-www-form-urlencoded" in content_type:
            try:
                form = await request.form()
                for key, value in form.items():
                    body[key] = str(value)
            except Exception as e:
                body = {"error": f"Error reading form data: {str(e)}"}
        
        return body
    
    def _mask_sensitive_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        遮蔽敏感数据
        
        Args:
            data: 原始数据
            
        Returns:
            Dict[str, Any]: 遮蔽后的数据
        """
        if not isinstance(data, dict):
            return data
            
        masked_data = {}
        for key, value in data.items():
            if isinstance(key, str) and any(sensitive in key.lower() for sensitive in self.sensitive_fields):
                masked_data[key] = "******"
            elif isinstance(value, dict):
                masked_data[key] = self._mask_sensitive_data(value)
            elif isinstance(value, list):
                masked_data[key] = [
                    self._mask_sensitive_data(item) if isinstance(item, dict) else item
                    for item in value
                ]
            else:
                masked_data[key] = value
                
        return masked_data

    def _safe_json_dumps(self, data: Dict[str, Any]) -> str:
        """
        安全的JSON序列化，处理不可序列化的对象

        Args:
            data: 要序列化的数据

        Returns:
            str: JSON字符串
        """
        def json_serializer(obj):
            """自定义JSON序列化器"""
            if hasattr(obj, 'isoformat'):
                # 处理datetime对象
                return obj.isoformat()
            elif hasattr(obj, '__dict__'):
                # 处理自定义对象
                return str(obj)
            else:
                # 其他不可序列化对象
                return str(obj)

        try:
            return json.dumps(
                data,
                ensure_ascii=False,
                separators=(',', ':'),
                default=json_serializer
            )
        except Exception as e:
            # 如果序列化失败，返回错误信息
            return json.dumps({
                "error": "JSON序列化失败",
                "error_message": str(e),
                "data_type": str(type(data))
            }, ensure_ascii=False, separators=(',', ':'))