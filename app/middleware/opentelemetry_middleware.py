"""
OpenTelemetry Trace 中间件
完全基于OpenTelemetry标准的trace context传播和管理
"""

from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from starlette.requests import Request
from starlette.responses import Response
from starlette.types import ASGIApp

from opentelemetry import trace
from opentelemetry.trace.propagation.tracecontext import TraceContextTextMapPropagator
from opentelemetry.context import attach, detach
from opentelemetry.trace import SpanKind, Status, StatusCode

from app.core.telemetry import SpanLogger


class OpenTelemetryTraceMiddleware(BaseHTTPMiddleware):
    """
    OpenTelemetry Trace 中间件
    
    功能：
    1. 从请求头中提取OpenTelemetry trace context
    2. 为每个HTTP请求创建root span
    3. 传播trace context到下游服务
    4. 在响应头中返回trace信息
    5. 自动记录请求的trace信息到日志
    """

    def __init__(self, app: ASGIApp, service_name: str = "ai-platform"):
        super().__init__(app)
        self.service_name = service_name
        self.propagator = TraceContextTextMapPropagator()
        self.tracer = trace.get_tracer(__name__)

    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        # 1. 从请求头中提取trace context
        parent_context = self.propagator.extract(request.headers)
        context_token = attach(parent_context)
        
        # 2. 创建HTTP请求的root span
        span_name = f"{request.method} {request.url.path}"
        
        with self.tracer.start_as_current_span(
            span_name,
            kind=SpanKind.SERVER,
            attributes={
                "http.method": request.method,
                "http.url": str(request.url),
                "http.scheme": request.url.scheme,
                "http.host": request.url.hostname or "localhost",
                "http.target": request.url.path,
                "http.user_agent": request.headers.get("user-agent", ""),
                "service.name": self.service_name,
            }
        ) as span:
            
            # 获取trace信息
            span_context = span.get_span_context()
            trace_id = f"{span_context.trace_id:032x}"
            span_id = f"{span_context.span_id:016x}"
            
            # 记录span开始
            SpanLogger.log_span_start(
                span_name, 
                {
                    "http.method": request.method,
                    "http.url": str(request.url),
                    "request.headers": dict(request.headers)
                }
            )
            
            try:
                # 3. 处理请求
                response = await call_next(request)
                
                # 4. 设置span属性
                span.set_attribute("http.status_code", response.status_code)
                
                # 设置span状态
                if response.status_code >= 400:
                    span.set_status(Status(StatusCode.ERROR, f"HTTP {response.status_code}"))
                else:
                    span.set_status(Status(StatusCode.OK))
                
                # 5. 记录span结束
                SpanLogger.log_span_end(
                    span_name,
                    status="success" if response.status_code < 400 else "error",
                    attributes={
                        "http.status_code": response.status_code,
                        "response.headers": dict(response.headers)
                    }
                )
                
                # 6. 在响应头中添加trace信息
                response.headers["x-trace-id"] = trace_id
                response.headers["x-span-id"] = span_id
                
                return response
                
            except Exception as e:
                # 记录异常
                span.set_status(Status(StatusCode.ERROR, str(e)))
                SpanLogger.log_span_error(e, {"request.url": str(request.url)})
                raise
                
            finally:
                # 清理context
                detach(context_token)


class OpenTelemetryInstrumentationMiddleware(BaseHTTPMiddleware):
    """
    OpenTelemetry 自动化仪表盘中间件
    提供更详细的HTTP请求追踪
    """
    
    def __init__(self, app: ASGIApp, service_name: str = "ai-platform"):
        super().__init__(app)
        self.service_name = service_name
        self.tracer = trace.get_tracer(__name__)
    
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        # 获取当前span（应该由TraceMiddleware创建）
        current_span = trace.get_current_span()
        
        if current_span and current_span.is_recording():
            # 添加更多的请求属性
            current_span.set_attribute("http.request.size", 
                                     int(request.headers.get("content-length", 0)))
            
            # 记录请求开始事件
            SpanLogger.log_span_event(
                "request.start",
                {
                    "http.method": request.method,
                    "http.path": request.url.path,
                    "http.query": str(request.url.query) if request.url.query else None
                }
            )
        
        # 处理请求
        response = await call_next(request)
        
        if current_span and current_span.is_recording():
            # 添加响应属性
            response_size = 0
            if hasattr(response, 'body'):
                response_size = len(response.body)
            elif 'content-length' in response.headers:
                response_size = int(response.headers['content-length'])
            
            current_span.set_attribute("http.response.size", response_size)
            
            # 记录请求完成事件
            SpanLogger.log_span_event(
                "request.complete",
                {
                    "http.status_code": response.status_code,
                    "http.response.size": response_size
                }
            )
        
        return response



