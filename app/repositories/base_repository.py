"""
Repository基础类
基于CRUDBase重构，提供统一的数据访问接口
"""
from typing import Any, Dict, Generic, List, Optional, Type, TypeVar, Union
import json
from datetime import datetime

from fastapi.encoders import jsonable_encoder
from pydantic import BaseModel
from sqlalchemy import select, update, delete, func
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.base import Base
from app.utils.snowflake import generate_snowflake_id
from app.log import logger

# 定义类型变量
ModelType = TypeVar("ModelType", bound=Base)
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)


class BaseRepository(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    """
    提供基本数据访问操作的基类
    
    Args:
        model: SQLAlchemy数据库模型
    """

    def __init__(self, model: Type[ModelType]):
        """
        初始化Repository类
        
        Args:
            model: 数据库模型类
        """
        self.model = model

    def ensure_dict(self, data: Any) -> Dict[str, Any]:
        """
        确保输入数据是一个字典类型
        
        Args:
            data: 任意输入数据
                
        Returns:
            Dict: 转换后的字典，如果输入为 None 则返回 None
        """
        if data is None:
            return None  # 返回 None 而不是空字典
            
        if isinstance(data, dict):
            return data
            
        if isinstance(data, str):
            try:
                return json.loads(data)
            except json.JSONDecodeError:
                logger.warning(f"无法将字符串解析为JSON: {data[:100]}...")
                return {}
                
        # 其他类型，尝试转换为字典
        try:
            return dict(data)
        except (TypeError, ValueError):
            logger.warning(f"无法将类型 {type(data)} 转换为字典")
            return {}

    def process_jsonb_fields(self, obj: Any) -> Any:
        """
        处理JSONB字段，确保字典类型的字段被正确处理
        
        Args:
            obj: 数据库对象或其他对象
            
        Returns:
            处理后的对象
        """
        if obj is None:
            return None
            
        # 如果是数据库模型实例
        if hasattr(obj, '__table__'):
            for column in obj.__table__.columns:
                if hasattr(column.type, 'python_type') and column.type.python_type == dict:
                    value = getattr(obj, column.name)
                    if value is not None:
                        setattr(obj, column.name, self.ensure_dict(value))
            return obj
            
        # 如果是字典
        if isinstance(obj, dict):
            return {k: self.ensure_dict(v) if isinstance(v, (str, dict)) else v for k, v in obj.items()}
            
        # 如果是列表
        if isinstance(obj, list):
            return [self.process_jsonb_fields(item) for item in obj]

        return obj

    async def get(self, db: AsyncSession, id: Any) -> Optional[ModelType]:
        """
        根据ID获取数据库记录
        
        Args:
            db: 数据库会话
            id: 记录ID
            
        Returns:
            Optional[ModelType]: 数据库记录或None
        """
        result = await db.execute(select(self.model).where(self.model.id == id))
        obj = result.scalars().first()
        if obj:
            return self.process_jsonb_fields(obj)
        return None

    async def get_multi(
        self,
        db: AsyncSession,
        *,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[ModelType]:
        """
        获取多条记录
        
        Args:
            db: 数据库会话
            skip: 跳过记录数
            limit: 返回记录数上限
            filters: 过滤条件字典
            
        Returns:
            List[ModelType]: 记录列表
        """
        query = select(self.model)
        
        # 添加过滤条件
        if filters:
            for field, value in filters.items():
                if hasattr(self.model, field):
                    if isinstance(value, list):
                        query = query.where(getattr(self.model, field).in_(value))
                    else:
                        query = query.where(getattr(self.model, field) == value)
        
        # 分页
        query = query.offset(skip).limit(limit)
        
        # 执行查询
        result = await db.execute(query)
        objs = result.scalars().all()
        
        # 处理JSONB字段
        return [self.process_jsonb_fields(obj) for obj in objs]

    def _preprocess_create_data(self, obj_in: Union[CreateSchemaType, Dict[str, Any]]) -> Dict[str, Any]:
        """
        预处理创建数据
        
        Args:
            obj_in: 创建数据模型或字典
            
        Returns:
            Dict[str, Any]: 预处理后的数据字典
        """
        if isinstance(obj_in, dict):
            obj_in_data = obj_in
        else:
            # 使用 model_dump() 而不是 dict() 避免 CamelModel 产生驼峰命名的键
            obj_in_data = obj_in.model_dump(exclude_unset=True)
        
        # 预处理JSONB字段
        obj_in_data = self.process_jsonb_fields(obj_in_data)
        
        return obj_in_data

    async def _create_model_instance(self, processed_data: Dict[str, Any], id_field: str = "id") -> ModelType:
        """
        创建模型实例
        
        Args:
            processed_data: 预处理后的数据
            id_field: ID字段名
            
        Returns:
            ModelType: 模型实例
        """
        # 如果没有提供ID且模型有id字段，生成雪花ID
        if id_field in processed_data and processed_data[id_field] is None:
            del processed_data[id_field]
        
        if id_field not in processed_data and hasattr(self.model, id_field):
            processed_data[id_field] = generate_snowflake_id()
        
        return self.model(**processed_data)

    async def create(self, db: AsyncSession, *, obj_in: Union[CreateSchemaType, Dict[str, Any]], id_field: str = "id") -> ModelType:
        """
        创建记录

        Args:
            db: 数据库会话
            obj_in: 创建数据模型或字典
            id_field: ID字段名，默认为"id"

        Returns:
            ModelType: 创建的数据库记录
        """
        # 使用公共预处理方法
        processed_data = self._preprocess_create_data(obj_in)

        # 使用公共模型实例创建方法
        db_obj = await self._create_model_instance(processed_data, id_field)

        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)

        # 处理JSONB字段
        return self.process_jsonb_fields(db_obj)

    async def update(
        self,
        db: AsyncSession,
        *,
        db_obj: ModelType,
        obj_in: Union[UpdateSchemaType, Dict[str, Any]]
    ) -> ModelType:
        """
        更新记录
        
        Args:
            db: 数据库会话
            db_obj: 要更新的数据库对象
            obj_in: 更新数据模型或字典
            
        Returns:
            ModelType: 更新后的数据库记录
        """
        obj_data = jsonable_encoder(db_obj)
        
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            # 使用 model_dump() 而不是 dict() 避免 CamelModel 产生驼峰命名的键
            update_data = obj_in.model_dump(exclude_unset=True)
        
        # 预处理更新数据中的JSONB字段
        update_data = self.process_jsonb_fields(update_data)
        
        for field in obj_data:
            if field in update_data:
                setattr(db_obj, field, update_data[field])
                
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        
        # 处理JSONB字段
        return self.process_jsonb_fields(db_obj)

    async def delete(self, db: AsyncSession, *, id: int) -> ModelType:
        """
        删除记录
        
        Args:
            db: 数据库会话
            id: 记录ID
            
        Returns:
            ModelType: 被删除的记录
        """
        obj = await db.execute(select(self.model).where(self.model.id == id))
        obj = obj.scalars().first()
        await db.delete(obj)
        await db.commit()
        return obj
        
    async def count(
        self,
        db: AsyncSession,
        *,
        filters: Optional[Dict[str, Any]] = None
    ) -> int:
        """
        计算符合条件的记录数
        
        Args:
            db: 数据库会话
            filters: 过滤条件字典
            
        Returns:
            int: 记录数量
        """
        query = select(func.count()).select_from(self.model)
        
        # 添加过滤条件
        if filters:
            for field, value in filters.items():
                if hasattr(self.model, field):
                    if isinstance(value, list):
                        query = query.where(getattr(self.model, field).in_(value))
                    else:
                        query = query.where(getattr(self.model, field) == value)
        
        # 执行查询
        result = await db.execute(query)
        return result.scalar()
