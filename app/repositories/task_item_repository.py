"""任务数据访问层"""
from typing import List, Optional, Dict, Any
from datetime import datetime

from sqlalchemy import select, and_, or_, func
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.task_item import TaskItem
from app.schemas.task import TaskItemCreate, TaskItemUpdate
from app.repositories.base_repository import BaseRepository


class TaskItemRepository(BaseRepository[TaskItem, TaskItemCreate, TaskItemUpdate]):
    """任务项数据访问类"""
    
    def __init__(self):
        """初始化任务项数据访问类"""
        super().__init__(TaskItem)
        
    async def get_by_task_id(
        self,
        db: AsyncSession,
        *,
        task_id: int
    ) -> List[TaskItem]:
        """
        根据任务ID获取任务项列表
        
        Args:
            db: 数据库会话
            task_id: 任务ID
            
        Returns:
            List[TaskItem]: 任务项列表
        """
        result = await db.execute(
            select(self.model)
            .where(self.model.task_id == task_id)
            .order_by(self.model.id)
        )
        return result.scalars().all()
        
    async def get_by_task_id_and_status(
        self,
        db: AsyncSession,
        *,
        task_id: int,
        status: str
    ) -> List[TaskItem]:
        """
        根据任务ID和状态获取任务项列表
        
        Args:
            db: 数据库会话
            task_id: 任务ID
            status: 状态
            
        Returns:
            List[TaskItem]: 任务项列表
        """
        result = await db.execute(
            select(self.model)
            .where(
                and_(
                    self.model.task_id == task_id,
                    self.model.status == status
                )
            )
            .order_by(self.model.id)
        )
        return result.scalars().all()
        
    async def get_pending_items(
        self,
        db: AsyncSession,
        *,
        limit: int = 100
    ) -> List[TaskItem]:
        """
        获取待处理的任务项
        
        Args:
            db: 数据库会话
            limit: 返回记录数上限
            
        Returns:
            List[TaskItem]: 待处理的任务项列表
        """
        result = await db.execute(
            select(self.model)
            .where(self.model.status == "pending")
            .order_by(self.model.id)
            .limit(limit)
        )
        return result.scalars().all()
        
    async def update_status(
        self,
        db: AsyncSession,
        *,
        item_id: int,
        status: str,
        error: Optional[str] = None,
        result: Optional[Dict[str, Any]] = None,
        process_time: Optional[Dict[str, Any]] = None
    ) -> Optional[TaskItem]:
        """
        更新任务项状态
        
        Args:
            db: 数据库会话
            item_id: 任务项ID
            status: 新状态
            error: 错误信息
            result: 处理结果
            process_time: 处理时间
            
        Returns:
            Optional[TaskItem]: 更新后的任务项或None
        """
        result_obj = await db.execute(
            select(self.model).where(self.model.id == item_id)
        )
        item = result_obj.scalars().first()
        if not item:
            return None
            
        # 更新状态
        item.status = status
        item.updated_at = datetime.now()
        
        # 根据状态更新相应字段
        if status == "completed":
            item.completed_at = datetime.now()
            if result is not None:
                item.result = result
            if process_time is not None:
                item.process_time = process_time
        elif status == "failed":
            item.completed_at = datetime.now()
            if error is not None:
                item.error = error
            if process_time is not None:
                item.process_time = process_time
        elif status == "processing":
            item.started_at = datetime.now()
            
        # 更新错误信息
        if error is not None:
            item.error = error
            
        # 更新结果
        if result is not None:
            item.result = result
            
        # 更新处理时间
        if process_time is not None:
            item.process_time = process_time
            
        db.add(item)
        await db.commit()
        await db.refresh(item)
        
        return item

    async def count_by_task_id(
        self,
        db: AsyncSession,
        *,
        task_id: int
    ) -> int:
        """
        根据任务ID统计任务项数量
        
        Args:
            db: 数据库会话
            task_id: 任务ID
            
        Returns:
            int: 任务项数量
        """
        result = await db.execute(
            select(func.count(self.model.id))
            .where(self.model.task_id == task_id)
        )
        return result.scalar_one()

    async def update_direct(
        self,
        db: AsyncSession,
        *,
        id: int,
        update_data: Dict[str, Any]
    ) -> bool:
        """
        直接更新任务项（不先查询，用于性能优化）

        Args:
            db: 数据库会话
            id: 任务项ID
            update_data: 更新数据

        Returns:
            bool: 是否更新成功（影响行数 > 0）
        """
        from sqlalchemy import update

        # 构建更新语句
        update_stmt = (
            update(self.model)
            .where(self.model.id == id)
            .values(**update_data)
        )

        # 执行更新
        result = await db.execute(update_stmt)
        await db.commit()

        # 返回是否有行被更新
        return result.rowcount > 0

    async def get_by_task_id_paginated(
        self,
        db: AsyncSession,
        *,
        task_id: int,
        skip: int = 0,
        limit: int = 100
    ) -> List[TaskItem]:
        """
        分页获取任务的任务项

        Args:
            db: 数据库会话
            task_id: 任务ID
            skip: 跳过记录数
            limit: 返回记录数上限

        Returns:
            List[TaskItem]: 任务项列表
        """
        result = await db.execute(
            select(self.model)
            .where(self.model.task_id == task_id)
            .order_by(self.model.id)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    async def count_by_task_id(
        self,
        db: AsyncSession,
        *,
        task_id: int,
        status: Optional[List[str]] = None
    ) -> int:
        """
        根据任务ID和状态统计任务项数量

        Args:
            db: 数据库会话
            task_id: 任务ID
            status: 状态列表（可选）

        Returns:
            int: 任务项数量
        """
        query = select(func.count(self.model.id)).where(self.model.task_id == task_id)

        if status:
            query = query.where(self.model.status.in_(status))

        result = await db.execute(query)
        return result.scalar_one()


# 创建任务项数据访问实例
task_item_repository = TaskItemRepository()