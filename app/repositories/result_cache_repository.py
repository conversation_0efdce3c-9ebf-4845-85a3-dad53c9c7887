"""
结果缓存数据访问层
基于ResultCacheCRUD重构为Repository模式
"""
from typing import Optional

from sqlalchemy import select, and_
from sqlalchemy.ext.asyncio import AsyncSession

from app.repositories.base_repository import BaseRepository
from app.models.result_cache import ResultCache
from app.schemas.result_cache import ResultCacheCreate, ResultCacheUpdate


class ResultCacheRepository(BaseRepository[ResultCache, ResultCacheCreate, ResultCacheUpdate]):
    """结果缓存数据访问类"""
    
    def __init__(self):
        """初始化结果缓存数据访问类"""
        super().__init__(ResultCache)
    
    async def get_by_composite_key(
        self,
        db: AsyncSession,
        *,
        file_hash: str,
        plugin_code: str,
        plugin_version: str,
        params_hash: str
    ) -> Optional[ResultCache]:
        """
        根据组合键获取缓存
        
        Args:
            db: 数据库会话
            file_hash: 文件哈希
            plugin_code: 插件编码
            plugin_version: 插件版本
            params_hash: 参数哈希
            
        Returns:
            Optional[ResultCache]: 缓存对象或None
        """
        result = await db.execute(
            select(self.model)
            .where(
                and_(
                    self.model.file_hash == file_hash,
                    self.model.plugin_code == plugin_code,
                    self.model.plugin_version == plugin_version,
                    self.model.params_hash == params_hash
                )
            )
        )
        db_obj = result.scalar_one_or_none()
        if db_obj:
            # 处理JSONB字段
            return self.process_jsonb_fields(db_obj)
        return None


# 创建全局Repository实例
result_cache_repository = ResultCacheRepository()
