"""
插件服务层
基于PluginManager重构为Service模式
"""
import os
import shutil
from typing import Optional, Dict, Any, List, Tuple

from fastapi import HTTPException, UploadFile, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.config.settings import settings
from app.repositories.plugin_repository import plugin_repository
from app.core.plugin.parser import PluginParser, PluginParseError
from app.models.plugin import Plugin
from app.schemas.plugin import PluginUpdate, PluginFilter, PluginInfo, PluginStatus, \
    PluginCreate, PluginResponse
from app.schemas.base import PageResponse, PageParams
from app.log import plugin_logger as logger
from app.utils.file import get_plugin_path, get_plugin_temp_path, to_absolute_path, to_relative_path
from app.utils.version import is_newer_version


class PluginService:
    """插件服务类"""

    async def register_plugin(
            self,
            db: AsyncSession,
            *,
            model_file: Optional[UploadFile] = None,
            config_file: UploadFile,
            python_file: UploadFile
    ) -> Plugin:
        """
        注册新插件
        如果是新版本插件，会自动将旧版本的状态设置为disabled
        
        Args:
            db: 数据库会话
            model_file: 模型文件（对于_vlm插件可选）
            config_file: 配置文件
            python_file: Python处理逻辑文件
            
        Returns:
            Plugin: 注册的插件对象
        
        Raises:
            HTTPException: 注册失败时抛出
        """
        # 创建临时目录
        temp_dir = get_plugin_temp_path()
        os.makedirs(temp_dir, exist_ok=True)

        try:
            # 保存上传的文件到临时目录
            temp_config_path = os.path.join(temp_dir, config_file.filename)
            temp_python_path = os.path.join(temp_dir, python_file.filename)

            # 保存配置文件
            with open(temp_config_path, "wb") as f:
                content = await config_file.read()
                f.write(content)

            # 保存Python文件
            with open(temp_python_path, "wb") as f:
                content = await python_file.read()
                f.write(content)

            # 保存模型文件（如果提供）
            temp_model_path = None
            if model_file:
                temp_model_path = os.path.join(temp_dir, model_file.filename)
                with open(temp_model_path, "wb") as f:
                    content = await model_file.read()
                    f.write(content)

            # 解析插件配置
            plugin_info = PluginParser.parse_plugin(
                model_file_path=temp_model_path,
                config_file_path=temp_config_path,
                python_file_path=temp_python_path
            )

            plugin_code = plugin_info.plugin_code
            plugin_version = plugin_info.plugin_version

            # 检查插件是否已存在
            existing_plugin = await plugin_repository.get_by_code_version(
                db, plugin_code=plugin_code, plugin_version=plugin_version
            )

            if existing_plugin:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail=f"插件 {plugin_code}:{plugin_version} 已存在"
                )

            # 创建插件目录
            plugin_dir = get_plugin_path(plugin_code=plugin_code, plugin_version=plugin_version)
            os.makedirs(plugin_dir, exist_ok=True)

            # 移动文件到插件目录
            final_config_path = os.path.join(plugin_dir, "config.yaml")
            final_python_path = os.path.join(plugin_dir, "plugin.py")

            shutil.move(temp_config_path, final_config_path)
            shutil.move(temp_python_path, final_python_path)

            final_model_path = None
            if temp_model_path:
                final_model_path = os.path.join(plugin_dir, model_file.filename)
                shutil.move(temp_model_path, final_model_path)

            # 创建插件数据库记录
            plugin_create_obj = PluginCreate(
                plugin_code=plugin_info.plugin_code,
                plugin_version=plugin_info.plugin_version,
                name=plugin_info.name,
                description=plugin_info.description,
                type=plugin_info.type,
                engine=plugin_info.engine,
                input_file_type=plugin_info.input_file_type,
                input_media_type=plugin_info.input_media_type,
                classes=plugin_info.classes,
                author=plugin_info.author,
                status=PluginStatus.ENABLED
            )

            # 保存到数据库
            db_plugin = await plugin_repository.create_with_files(
                db,
                obj_in=plugin_create_obj,
                model_file_path=to_relative_path(final_model_path) if final_model_path else None,
                config_file_path=to_relative_path(final_config_path),
                python_file_path=to_relative_path(final_python_path),
            )

            logger.info(f"插件注册成功: {plugin_code}:{plugin_version}")

            # 禁用同一插件代码的旧版本
            old_versions = await plugin_repository.get_by_code(
                db,
                plugin_code=plugin_code,
                exclude_version=plugin_version
            )

            if old_versions:
                # 收集旧版本的ID
                old_version_ids = [p.id for p in old_versions]

                # 批量更新旧版本状态为disabled
                updated_count = await plugin_repository.update_status_by_ids(
                    db,
                    ids=old_version_ids,
                    status="disabled"
                )

                logger.info(
                    f"已自动禁用插件 {plugin_code} 的 {updated_count} 个旧版本，"
                    f"当前启用版本: {plugin_version}"
                )

            return db_plugin

        except PluginParseError as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"插件解析错误: {str(e)}"
            )
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"插件注册失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"插件注册失败: {str(e)}"
            )
        finally:
            # 清理临时目录
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir, ignore_errors=True)

    async def sync_plugin_from_path(
            self,
            db: AsyncSession,
            plugin_dir_path: str
    ) -> Plugin:
        """
        从本地路径同步插件
        
        Args:
            db: 数据库会话
            plugin_dir_path: 插件目录路径（相对于STORAGE_PATH）
            
        Returns:
            Plugin: 同步的插件对象
            
        Raises:
            HTTPException: 同步失败时抛出
        """
        try:
            # 构建绝对路径
            abs_plugin_dir = os.path.join(settings.STORAGE_PATH, plugin_dir_path)

            if not os.path.exists(abs_plugin_dir):
                raise PluginParseError(f"插件目录不存在: {abs_plugin_dir}")

            if not os.path.isdir(abs_plugin_dir):
                raise PluginParseError(f"指定路径不是一个目录: {abs_plugin_dir}")

            # 查找必要的文件
            model_path, config_path, python_path = PluginParser._find_plugin_files(abs_plugin_dir)

            # 解析配置文件获取插件编码和版本
            config = PluginParser.parse_config_file(config_path)
            plugin_code = config.get("plugin_code")
            plugin_version = config.get("plugin_version")

            if not plugin_code or not plugin_version:
                raise PluginParseError("配置文件缺少必要的插件编码或版本信息")

            # 检查是否是 VLM 插件
            is_vlm_plugin = plugin_code.endswith('_vlm')
            if is_vlm_plugin:
                model_path = None

            # 解析插件信息
            plugin_info = PluginParser.parse_plugin(
                model_file_path=model_path,
                config_file_path=config_path,
                python_file_path=python_path
            )

            # 检查插件是否已存在
            existing_plugin = await plugin_repository.get_by_code_version(
                db, plugin_code=plugin_code, plugin_version=plugin_version
            )

            # 转换为相对路径
            rel_config_path = to_relative_path(config_path)
            rel_python_path = to_relative_path(python_path)
            rel_model_path = to_relative_path(model_path) if model_path else None

            # 准备插件数据
            plugin_data = {
                "plugin_code": plugin_info.plugin_code,
                "plugin_version": plugin_info.plugin_version,
                "name": plugin_info.name,
                "description": plugin_info.description,
                "type": plugin_info.type,
                "engine": plugin_info.engine,
                "input_file_type": plugin_info.input_file_type,
                "input_media_type": plugin_info.input_media_type,
                "classes": plugin_info.classes,
                "author": plugin_info.author,
                "config_file_path": rel_config_path,
                "python_file_path": rel_python_path,
            }

            # 只有非VLM插件才设置模型文件路径
            if not is_vlm_plugin and rel_model_path:
                plugin_data["model_file_path"] = rel_model_path

            # 更新或创建插件
            if existing_plugin:
                plugin_data.update({
                    "config_file_path": rel_config_path,
                    "python_file_path": rel_python_path,
                    "status": existing_plugin.status
                })
                # 只有非VLM插件才更新模型文件路径
                if not is_vlm_plugin and rel_model_path:
                    plugin_data["model_file_path"] = rel_model_path
                db_plugin = await plugin_repository.update(db, db_obj=existing_plugin, obj_in=plugin_data)
                logger.info(f"插件更新成功: {plugin_code}:{plugin_version}")
            else:
                # 新插件默认为enabled状态
                plugin_data["status"] = "enabled"
                plugin_create_obj = PluginCreate(**plugin_data)

                # 创建新插件
                db_plugin = await plugin_repository.create_with_files(
                    db,
                    obj_in=plugin_create_obj,
                    model_file_path=rel_model_path,
                    config_file_path=rel_config_path,
                    python_file_path=rel_python_path
                )
                logger.info(f"插件同步成功: {plugin_code}:{plugin_version}")

            return db_plugin

        except PluginParseError as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"插件解析错误: {str(e)}"
            )
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"插件同步失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"插件同步失败: {str(e)}"
            )

    async def get_plugins(
            self,
            db: AsyncSession,
            *,
            filter_params: PluginFilter,
            page_params: PageParams
    ) -> PageResponse[PluginResponse]:
        """
        获取插件列表

        Args:
            db: 数据库会话
            filter_params: 过滤参数
            page_params: 分页参数

        Returns:
            PageResponse[PluginResponse]: 插件列表分页响应
        """
        # 获取分页数据
        plugins = await plugin_repository.get_filtered(
            db,
            filter_params=filter_params,
            skip=(page_params.page - 1) * page_params.page_size,
            limit=page_params.page_size
        )

        # 获取总数量
        total = await plugin_repository.count_filtered(db, filter_params=filter_params)

        # 转换为响应对象
        plugin_responses = [
            PluginResponse(
                id=plugin.id,
                plugin_code=plugin.plugin_code,
                plugin_version=plugin.plugin_version,
                name=plugin.name,
                description=plugin.description,
                type=plugin.type,
                engine=plugin.engine,
                input_file_type=plugin.input_file_type,
                input_media_type=plugin.input_media_type,
                classes=plugin.classes,
                author=plugin.author,
                status=plugin.status,
                created_at=plugin.created_at,
                updated_at=plugin.updated_at
            )
            for plugin in plugins
        ]

        return PageResponse[PluginResponse](
            items=plugin_responses,
            page=page_params.page,
            page_size=page_params.page_size,
            total_count=total,
            total_page=(total + page_params.page_size - 1) // page_params.page_size
        )

    async def get_plugin(self, db: AsyncSession, plugin_id: int) -> PluginResponse:
        """
        获取插件详情

        Args:
            db: 数据库会话
            plugin_id: 插件ID

        Returns:
            PluginResponse: 插件详情

        Raises:
            HTTPException: 获取失败时抛出
        """
        plugin = await plugin_repository.get(db, id=plugin_id)

        if not plugin or plugin.is_deleted:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="插件不存在"
            )

        return PluginResponse(
            id=plugin.id,
            plugin_code=plugin.plugin_code,
            plugin_version=plugin.plugin_version,
            name=plugin.name,
            description=plugin.description,
            type=plugin.type,
            engine=plugin.engine,
            input_file_type=plugin.input_file_type,
            input_media_type=plugin.input_media_type,
            classes=plugin.classes,
            author=plugin.author,
            status=plugin.status,
            created_at=plugin.created_at,
            updated_at=plugin.updated_at
        )

    async def update_plugin(
            self,
            db: AsyncSession,
            *,
            plugin_id: int,
            plugin_in: PluginUpdate
    ) -> PluginResponse:
        """
        更新插件信息

        Args:
            db: 数据库会话
            plugin_id: 插件ID
            plugin_in: 更新数据

        Returns:
            PluginResponse: 更新后的插件信息

        Raises:
            HTTPException: 更新失败时抛出
        """
        plugin = await plugin_repository.get(db, id=plugin_id)

        if not plugin or plugin.is_deleted:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="插件不存在"
            )

        # 更新插件
        updated_plugin = await plugin_repository.update(db, db_obj=plugin, obj_in=plugin_in)

        return PluginResponse(
            id=updated_plugin.id,
            plugin_code=updated_plugin.plugin_code,
            plugin_version=updated_plugin.plugin_version,
            name=updated_plugin.name,
            description=updated_plugin.description,
            type=updated_plugin.type,
            engine=updated_plugin.engine,
            input_file_type=updated_plugin.input_file_type,
            input_media_type=updated_plugin.input_media_type,
            classes=updated_plugin.classes,
            author=updated_plugin.author,
            status=updated_plugin.status,
            created_at=updated_plugin.created_at,
            updated_at=updated_plugin.updated_at
        )

    async def delete_plugin(self, db: AsyncSession, plugin_id: int) -> bool:
        """
        删除插件

        Args:
            db: 数据库会话
            plugin_id: 插件ID

        Returns:
            bool: 删除是否成功

        Raises:
            HTTPException: 删除失败时抛出
        """
        plugin = await plugin_repository.get(db, id=plugin_id)

        if not plugin or plugin.is_deleted:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="插件不存在"
            )

        # 软删除数据库记录
        await plugin_repository.soft_delete(db, db_obj=plugin)

        # 重命名插件目录，添加-deleted后缀
        old_dir = get_plugin_path(plugin_code=plugin.plugin_code, plugin_version=plugin.plugin_version)
        new_dir = get_plugin_path(plugin_code=plugin.plugin_code, plugin_version=plugin.plugin_version, is_deleted=True)

        try:
            if os.path.exists(old_dir):
                os.rename(old_dir, new_dir)
                logger.info(f"插件目录已重命名: {old_dir} -> {new_dir}")
        except Exception as e:
            logger.warning(f"重命名插件目录失败: {str(e)}")

        logger.info(f"插件删除成功: {plugin.plugin_code}:{plugin.plugin_version}")
        return True

    async def get_plugin_info(
        self,
        db: AsyncSession,
        *,
        plugin_code: str,
        plugin_version: str,
        absolute_path: bool = False
    ) -> Dict[str, Any]:
        """
        获取插件信息，用于插件执行

        Args:
            db: 数据库会话
            plugin_code: 插件编码
            plugin_version: 插件版本
            absolute_path: 是否返回绝对路径

        Returns:
            Dict[str, Any]: 插件信息字典

        Raises:
            HTTPException: 获取失败时抛出
        """
        # 获取插件数据库记录
        plugin = await plugin_repository.get_by_code_version(
            db, plugin_code=plugin_code, plugin_version=plugin_version
        )

        if not plugin or plugin.is_deleted:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"插件不存在: {plugin_code}:{plugin_version}"
            )

        # 构建插件信息
        plugin_info = {
            "plugin_code": plugin.plugin_code,
            "plugin_version": plugin.plugin_version,
            "name": plugin.name,
            "type": plugin.type,
            "engine": plugin.engine,
            "config_file_path": plugin.config_file_path,
            "python_file_path": plugin.python_file_path,
            "model_file_path": plugin.model_file_path
        }

        # 如果需要绝对路径，转换文件路径
        if absolute_path:
            from app.utils.file import to_absolute_path
            if plugin_info["config_file_path"]:
                plugin_info["config_file_path"] = to_absolute_path(plugin_info["config_file_path"])
            if plugin_info["python_file_path"]:
                plugin_info["python_file_path"] = to_absolute_path(plugin_info["python_file_path"])
            if plugin_info["model_file_path"]:
                plugin_info["model_file_path"] = to_absolute_path(plugin_info["model_file_path"])

        return plugin_info


# 创建全局Service实例
plugin_service = PluginService()
