"""
任务服务层
负责任务的创建、状态更新和查询
"""
from typing import Dict, List, Any, Optional
from datetime import datetime
import os

from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import HTTPException, status

from app.models.task import Task
from app.models.task_item import TaskItem
from app.repositories.task_repository import task_repository
from app.repositories.task_item_repository import task_item_repository
from app.repositories.plugin_repository import plugin_repository
from app.repositories.file_repository import file_repository
from app.repositories.result_cache_repository import result_cache_repository
from app.schemas.file import File
from app.schemas.task import TaskFilter, TaskItemCreate, TaskCreate, TaskResponse, TaskStatus, TaskItemResponse
from app.log import task_logger as logger
from app.utils.file import process_oss_url, to_absolute_path
from app.utils.trace_context_utils import create_task_with_context, traced_async_task, add_span_attributes, serialize_trace_context
from app.schemas.base import MediaType, PageResponse, PageParams
from app.db.session import get_async_db


class TaskService:
    """任务服务类"""

    async def create_task(
            self,
            db: AsyncSession,
            *,
            task_in: TaskCreate
    ) -> TaskResponse:
        """
        创建新任务
        
        Args:
            db: 数据库会话
            task_in: 任务创建参数
            
        Returns:
            Task: 创建的任务对象
            
        Raises:
            HTTPException: 创建失败时抛出
        """
        try:
            # 插件验证
            if not task_in.plugins or len(task_in.plugins) == 0:
                raise ValueError("未提供插件信息")

            # 验证所有插件是否存在且已启用
            for plugin_config in task_in.plugins:
                plugin_code = plugin_config.plugin_code
                plugin_version = plugin_config.plugin_version

                plugin = await plugin_repository.get_by_code_version(
                    db,
                    plugin_code=plugin_code,
                    plugin_version=plugin_version
                )

                if not plugin:
                    raise ValueError(f"插件 {plugin_code} 版本 {plugin_version} 不存在")

                if plugin.status != "enabled":
                    raise ValueError(f"插件 {plugin_code} 版本 {plugin_version} 未启用，当前状态: {plugin.status}")

            # 计算总任务项数量 = 任务项数量 × 插件数量（笛卡尔积）
            total_items_count = len(task_in.items) * len(task_in.plugins)

            # 1. 创建任务记录（初始状态为 initializing）
            task_data = {
                "total_items": total_items_count,
                "status": "initializing",  # 任务项创建中
                "priority": task_in.priority
            }
            # 添加数据库操作的span属性
            add_span_attributes(
                db_operation="task_create",
                total_items=len(task_in.items),
                total_plugins=len(task_in.plugins)
            )
            task = await task_repository.create(db, obj_in=task_data)

            # 启动异步任务处理任务项创建、状态更新和调度队列
            # 使用create_task_with_context确保trace context传播到异步任务
            create_task_with_context(
                self._process_task_items(
                    task.id,
                    task_in.items,
                    task_in.plugins,
                    task_in.priority
                ),
                name=f"process_task_items_{task.id}"
            )

            # 传入任务对象获取进度
            return await self.get_task_progress(db, task=task)

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"创建任务失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"创建任务失败: {str(e)}"
            )

    @traced_async_task(
        "task_items_processing_{task_id}",
        attributes={
            "component": "task_service",
            "operation": "process_task_items"
        }
    )
    async def _process_task_items(
            self,
            task_id: int,
            items: List[TaskItemCreate],
            plugins: List[Any],
            priority: int
    ):
        """
        后台处理任务项创建、状态更新和调度队列

        Args:
            task_id: 任务ID
            items: 任务项列表
            plugins: 插件列表
            priority: 任务优先级
        """
        try:
            # 添加span属性用于追踪
            add_span_attributes(
                task_id=task_id,
                total_items=len(items),
                total_plugins=len(plugins),
                priority=priority
            )

            # 创建会话
            from app.core.task.scheduler import scheduler
            from app.config.settings import settings
            from app.core.task.redis_queue import redis_queue

            # 使用批量创建方式
            task_items_to_create = []
            completed_items_count = 0

            # 预加载插件信息到缓存
            plugin_cache = {}
            for plugin_config in plugins:
                plugin_code = plugin_config.plugin_code
                plugin_version = plugin_config.plugin_version

                # 从数据库获取插件信息
                plugin = await plugin_repository.get_by_code_version(
                    None,  # 这里需要一个新的数据库会话
                    plugin_code=plugin_code,
                    plugin_version=plugin_version
                )

                if plugin:
                    plugin_cache[f"{plugin_code}:{plugin_version}"] = plugin

            # 预加载文件缓存
            file_cache = {}

            # 预加载结果缓存
            caches_map = {}
            from app.utils.hash import calculate_params_hash
            from sqlalchemy import select
            async with get_async_db() as db:
                # 构建缓存键列表
                cache_keys = []
                for item in items:
                    for plugin_config in plugins:
                        plugin_code = plugin_config.plugin_code
                        plugin_version = plugin_config.plugin_version
                        params_hash = calculate_params_hash(plugin_config.params)
                        cache_key = f"{item.data_id}:{plugin_code}:{plugin_version}:{params_hash}"
                        cache_keys.append(cache_key)

                # 批量查询缓存
                if cache_keys:
                    from app.models.result_cache import ResultCache
                    from datetime import datetime
                    result = await db.execute(
                        select(ResultCache)
                        .where(
                            ResultCache.cache_key.in_(cache_keys),
                            (ResultCache.expires_at.is_(None)) |
                            (ResultCache.expires_at > datetime.now())
                        )
                    )
                    caches = result.scalars().all()
                    caches_map = {cache.cache_key: cache for cache in caches}

                # 处理每个任务项
                for item in items:
                    # 处理文件URL
                    file_url = item.file_url
                    file_download_error = None
                    file = None
                    file_media_type = item.media_type.value if isinstance(item.media_type, MediaType) else item.media_type

                    if file_url.startswith("oss://"):
                        file_url = process_oss_url(file_url)
                    else:
                        file_url = to_absolute_path(file_url)

                    # 检查文件是否已下载
                    if file_url in file_cache:
                        file = file_cache[file_url]
                    else:
                        from app.core.file.manager import file_manager
                        try:
                            # 下载文件
                            file = await file_manager.download_file(db, url=file_url)
                            file_cache[file_url] = file
                        except Exception as e:
                            file_download_error = str(e)
                            logger.error(f"文件下载失败: {file_url}, 错误: {file_download_error}")

                    # 为每个插件创建任务项数据
                    for plugin_config in plugins:
                        plugin_code = plugin_config.plugin_code
                        plugin_version = plugin_config.plugin_version

                        # 初始化任务项数据的公共部分
                        task_item_data = {
                            "task_id": task_id,
                            "data_id": item.data_id,
                            "file_type": item.file_type,
                            "media_type": file_media_type,
                            "file_url": file_url,
                            "plugin_code": plugin_code,
                            "plugin_version": plugin_version,
                            "params": plugin_config.params,
                            "from_cache": False,
                        }

                        # 如果文件下载失败，添加失败状态的任务项到批量创建列表
                        if file_download_error:
                            error_message = f"文件下载失败: {file_download_error}"
                            task_item_data.update({
                                "status": "failed",
                                "error": error_message,
                                "file_id": None
                            })
                            task_items_to_create.append(task_item_data)
                            completed_items_count += 1  # 失败的任务项也算作已完成
                            continue

                        # 文件下载成功，继续正常处理流程
                        task_item_data["file_id"] = file.id if file else None
                        task_item_data["file_hash"] = file.file_hash if file else None

                        # 从缓存中获取插件信息
                        plugin_key = f"{plugin_code}:{plugin_version}"
                        plugin = plugin_cache.get(plugin_key)

                        # 判断插件是否存在
                        if not plugin:
                            error_message = f"插件 {plugin_code}:{plugin_version} 不存在"
                            logger.warning(error_message)

                            # 添加失败状态的任务项到批量创建列表
                            task_item_data.update({
                                "status": "failed",
                                "error": error_message
                            })
                            task_items_to_create.append(task_item_data)
                            completed_items_count += 1  # 失败的任务项也算作已完成
                            continue

                        # 判断插件是否启用
                        if plugin.status != "enabled":
                            error_message = f"插件 {plugin_code}:{plugin_version} 未启用，当前状态: {plugin.status}"
                            logger.warning(error_message)

                            # 添加失败状态的任务项到批量创建列表
                            task_item_data.update({
                                "status": "failed",
                                "error": error_message
                            })
                            task_items_to_create.append(task_item_data)
                            completed_items_count += 1  # 失败的任务项也算作已完成
                            continue

                        # 检查文件的媒体类型是否在插件支持的媒体类型列表中
                        plugin_media_types = plugin.input_media_type
                        # 确保是列表类型
                        if not isinstance(plugin_media_types, list):
                            logger.warning(
                                f"插件 {plugin_code}:{plugin_version} 的媒体类型不是列表格式: {plugin_media_types}")
                            plugin_media_types = []

                        if not plugin_media_types or file_media_type not in plugin_media_types:
                            error_message = f"媒体类型不匹配: 文件={file.file_hash if file else 'unknown'}, 媒体类型={file_media_type}, 插件={plugin_code}:{plugin_version}, 支持的媒体类型={plugin_media_types}"
                            logger.info(error_message)

                            # 添加失败状态的任务项到批量创建列表
                            task_item_data.update({
                                "status": "failed",
                                "error": error_message
                            })
                            task_items_to_create.append(task_item_data)
                            completed_items_count += 1  # 失败的任务项也算作已完成
                            continue

                        # 从预加载的缓存数据中查找
                        params_hash = calculate_params_hash(plugin_config.params)
                        cache_key = f"{file.file_hash if file else item.data_id}:{plugin_code}:{plugin_version}:{params_hash}"

                        cache = None
                        if cache_key in caches_map:
                            cache_obj = caches_map[cache_key]
                            cache = {
                                "result": cache_obj.result,
                                "process_time": cache_obj.process_time
                            }

                        # 检查缓存
                        if cache:
                            # 从缓存创建任务项
                            task_item_data.update({
                                "status": "completed",
                                "result": cache["result"],
                                "process_time": cache["process_time"],
                                "from_cache": True
                            })
                            task_items_to_create.append(task_item_data)
                            completed_items_count += 1
                        else:
                            # 添加待处理的任务项到批量创建列表
                            task_item_data["status"] = "pending"
                            task_items_to_create.append(task_item_data)

                # 批量创建任务项
                if task_items_to_create:
                    for task_item_data in task_items_to_create:
                        await task_item_repository.create(db, obj_in=task_item_data)

                # 更新任务状态
                from sqlalchemy import select
                result_obj = await db.execute(select(Task).where(Task.id == task_id))
                task = result_obj.scalars().first()
                if task:
                    task.status = "pending"
                    task.updated_at = datetime.now()
                    db.add(task)
                    await db.commit()

                # 添加到调度队列
                await scheduler.add_task(task_id, priority)

                logger.info(f"任务项处理完成: task_id={task_id}, "
                            f"总任务项={len(task_items_to_create)}, "
                            f"已完成={completed_items_count}, "
                            f"待处理={len(task_items_to_create) - completed_items_count}")

        except Exception as e:
            logger.error(f"处理任务项失败: {str(e)}", exc_info=True)
            # 更新任务状态为失败
            try:
                async with get_async_db() as db:
                    from sqlalchemy import select
                    result_obj = await db.execute(select(Task).where(Task.id == task_id))
                    task = result_obj.scalars().first()
                    if task:
                        task.status = "failed"
                        task.updated_at = datetime.now()
                        db.add(task)
                        await db.commit()
            except Exception as update_error:
                logger.error(f"更新任务状态失败: {str(update_error)}")

    async def get_task(
            self,
            db: AsyncSession,
            *,
            task_id: int
    ) -> TaskResponse:
        """
        获取任务详情
        
        Args:
            db: 数据库会话
            task_id: 任务ID
            
        Returns:
            TaskResponse: 任务详情响应
        """
        task = await task_repository.get(db, id=task_id)
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在"
            )

        return await self.get_task_progress(db, task=task)

    async def get_tasks(
            self,
            db: AsyncSession,
            *,
            filter_params: TaskFilter,
            page_params: PageParams
    ) -> PageResponse[TaskResponse]:
        """
        获取任务列表
        
        Args:
            db: 数据库会话
            filter_params: 过滤参数
            page_params: 分页参数
            
        Returns:
            PageResponse[TaskResponse]: 任务列表分页响应
        """
        # 获取分页数据
        tasks = await task_repository.get_filtered(
            db,
            filter_params=filter_params,
            skip=(page_params.page - 1) * page_params.page_size,
            limit=page_params.page_size
        )

        # 获取总数量
        total = await task_repository.count_filtered(db, filter_params=filter_params)

        # 转换为响应对象
        task_responses = []
        for task in tasks:
            task_response = await self.get_task_progress(db, task=task)
            task_responses.append(task_response)

        return PageResponse[TaskResponse](
            items=task_responses,
            total=total,
            page=page_params.page,
            page_size=page_params.page_size,
            total_count=total,
            total_page=(total + page_params.page_size - 1) // page_params.page_size
        )

    async def get_task_progress(
            self,
            db: AsyncSession,
            *,
            task: Task
    ) -> TaskResponse:
        """
        获取任务进度
        
        Args:
            db: 数据库会话
            task: 任务对象
            
        Returns:
            TaskResponse: 任务进度响应
        """
        # 获取任务项统计信息
        from sqlalchemy import text
        result = await db.execute(
            text("""
                SELECT 
                    status,
                    COUNT(*) as count
                FROM task_item 
                WHERE task_id = :task_id 
                GROUP BY status
            """),
            {"task_id": task.id}
        )
        status_counts = result.fetchall()

        # 统计各状态数量
        status_count_map = {row[0]: row[1] for row in status_counts}

        pending_count = status_count_map.get("pending", 0)
        processing_count = status_count_map.get("processing", 0)
        completed_count = status_count_map.get("completed", 0)
        failed_count = status_count_map.get("failed", 0)

        # 计算进度百分比
        total_items = task.total_items or 0
        progress = 0.0
        if total_items > 0:
            progress = round((completed_count + failed_count) / total_items * 100, 2)

        # 确定任务状态
        if task.status in ["initializing", "pending"]:
            if pending_count + processing_count + completed_count + failed_count == 0:
                current_status = TaskStatus.INITIALIZING
            elif completed_count + failed_count == total_items:
                # 检查是否有失败项
                if failed_count > 0:
                    current_status = TaskStatus.FAILED
                else:
                    current_status = TaskStatus.COMPLETED
            elif processing_count > 0 or (pending_count < total_items and pending_count > 0):
                current_status = TaskStatus.PROCESSING
            else:
                current_status = TaskStatus.PENDING
        else:
            current_status = TaskStatus(task.status)

        return TaskResponse(
            id=task.id,
            total_items=task.total_items,
            processed_items=completed_count + failed_count,
            pending_items=pending_count,
            processing_items=processing_count,
            completed_items=completed_count,
            failed_items=failed_count,
            progress=progress,
            status=current_status,
            priority=task.priority,
            created_at=task.created_at,
            updated_at=task.updated_at,
            started_at=task.started_at,
            completed_at=task.completed_at
        )

    async def update_task_status(
            self,
            db: AsyncSession,
            *,
            task_id: int,
            status: str,
            error: Optional[str] = None
    ) -> Task:
        """
        更新任务状态
        
        Args:
            db: 数据库会话
            task_id: 任务ID
            status: 新状态
            error: 错误信息
            
        Returns:
            Task: 更新后的任务对象
        """
        task = await task_repository.get(db, id=task_id)
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在"
            )

        update_data = {
            "status": status,
            "updated_at": datetime.now()
        }

        # 更新时间戳
        if status == "running" and not task.started_at:
            update_data["started_at"] = datetime.now()
        elif status in ["completed", "failed", "canceled"]:
            update_data["completed_at"] = datetime.now()

        updated_task = await task_repository.update(db, db_obj=task, obj_in=update_data)
        return updated_task

    async def update_task_priority(
            self,
            db: AsyncSession,
            *,
            task_id: int,
            priority: int
    ) -> TaskResponse:
        """
        更新任务优先级
        
        Args:
            db: 数据库会话
            task_id: 任务ID
            priority: 新的优先级
            
        Returns:
            Task: 更新后的任务对象
        """
        task = await task_repository.get(db, id=task_id)
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在"
            )

        if task.status in ["completed", "failed", "canceled"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"任务已{task.status}，无法调整优先级"
            )

        updated_task = await task_repository.update(
            db,
            db_obj=task,
            obj_in={
                "priority": priority,
                "updated_at": datetime.now()
            }
        )

        # 更新调度队列中的优先级
        if updated_task.status in ["pending", "running"]:
            from app.core.task.scheduler import scheduler
            await scheduler.add_task(updated_task.id, priority)

        # 传入任务对象获取进度
        return await self.get_task_progress(db, task=updated_task)

    async def cancel_task(
            self,
            db: AsyncSession,
            *,
            task_id: int
    ) -> TaskResponse:
        """
        取消任务

        Args:
            db: 数据库会话
            task_id: 任务ID

        Returns:
            任务信息

        Raises:
            ValueError: 任务不存在或任务状态不允许取消
        """
        # 1. 获取任务信息
        task = await task_repository.get(db, id=task_id)
        if not task:
            raise ValueError("任务不存在")

        # 2. 检查任务状态是否允许取消
        if task.status in ["completed", "failed", "canceled"]:
            raise ValueError(f"任务状态为 {task.status}，不允许取消")
            
        # 3. 检查任务项是否已全部创建完成
        total_created_items = await task_item_repository.count_by_task_id(db, task_id=task_id)
        if total_created_items != task.total_items:
            raise ValueError(f"任务项尚未创建完成，已创建 {total_created_items}/{task.total_items}，不允许取消")

        # 4. 更新任务状态为已取消
        now = datetime.now()
        task.status = "canceled"
        task.completed_at = now
        task.updated_at = now
        await db.commit()

        # 5. 更新未完成的任务项状态为已取消
        # TODO: 实现更新任务项状态的方法

        # 6. 从调度队列中移除任务
        from app.core.task.scheduler import scheduler
        await scheduler.remove_task(task_id)

        # 7. 返回更新后的任务信息
        return await self.get_task_progress(db, task=task)

    async def get_pending_items(
            self,
            db: AsyncSession,
            *,
            task_id: int,
            limit: Optional[int] = None
    ) -> List[TaskItem]:
        """
        获取任务的待处理任务项
        
        Args:
            db: 数据库会话
            task_id: 任务ID
            limit: 返回数量限制
            
        Returns:
            List[TaskItem]: 待处理的任务项列表
        """
        return await task_item_repository.get_by_task_id_and_status(
            db,
            task_id=task_id,
            status="pending"
        )

    async def update_task_item(
            self,
            db: AsyncSession,
            *,
            id: int,
            update_data: Dict[str, Any]
    ) -> TaskItem:
        """
        更新任务项信息

        Args:
            db: 数据库会话
            id: 任务项ID
            update_data: 更新数据

        Returns:
            TaskItem: 更新后的任务项
        """
        item = await task_item_repository.get(db, id=id)
        if not item:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"任务项 {id} 不存在"
            )
        return await task_item_repository.update(db, db_obj=item, obj_in=update_data)

    async def get_task_item(
            self,
            db: AsyncSession,
            *,
            id: int
    ) -> Optional[TaskItem]:
        """获取单个任务项"""
        return await task_item_repository.get(db, id=id)

    async def get_task_model(self, db: AsyncSession, task_id: int) -> Optional[Task]:
        """
        获取任务模型对象

        Args:
            db: 数据库会话
            task_id: 任务ID

        Returns:
            Optional[Task]: 任务模型对象或None
        """
        return await task_repository.get(db, id=task_id)

    async def get_task_items(
            self,
            db: AsyncSession,
            *,
            task_id: int,
            page_params: PageParams
    ) -> PageResponse[TaskItemResponse]:
        """
        获取任务的所有任务项

        Args:
            db: 数据库会话
            task_id: 任务ID
            page_params: 分页参数

        Returns:
            PageResponse[TaskItemResponse]: 任务项列表分页响应
        """
        # 获取总数
        total = await task_item_repository.count_by_task_id(db, task_id=task_id)

        # 计算skip和limit
        skip = (page_params.page - 1) * page_params.page_size
        limit = page_params.page_size

        items = await task_item_repository.get_by_task_id_paginated(
            db,
            task_id=task_id,
            skip=skip,
            limit=limit
        )

        # 转换为响应对象
        task_item_responses = [
            TaskItemResponse(
                id=item.id,
                task_id=item.task_id,
                data_id=item.data_id,
                file_type=item.file_type,
                media_type=item.media_type,
                file_id=item.file_id,
                file_url=item.file_url,
                file_hash=item.file_hash,
                plugin_code=item.plugin_code,
                plugin_version=item.plugin_version,
                params=item.params,
                status=item.status,
                error=item.error,
                result=item.result,
                from_cache=item.from_cache,
                process_time=item.process_time,
                created_at=item.created_at,
                updated_at=item.updated_at,
                started_at=item.started_at,
                completed_at=item.completed_at
            )
            for item in items
        ]

        return PageResponse[TaskItemResponse](
            items=task_item_responses,
            page=page_params.page,
            page_size=page_params.page_size,
            total_count=total,
            total_page=(total + page_params.page_size - 1) // page_params.page_size
        )

    async def update_task_item_direct(
        self,
        db: AsyncSession,
        *,
        id: int,
        update_data: Dict[str, Any]
    ) -> bool:
        """
        直接更新任务项（不先查询，用于性能优化）

        Args:
            db: 数据库会话
            id: 任务项ID
            update_data: 更新数据

        Returns:
            bool: 是否更新成功（影响行数 > 0）
        """
        return await task_item_repository.update_direct(db, id=id, update_data=update_data)


# 创建全局任务服务实例
task_service = TaskService()