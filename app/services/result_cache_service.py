"""
缓存服务层
基于CacheManager重构为Service模式
"""
import json
from typing import Dict, Any, Optional

from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import HTTPException, status

from app.models.result_cache import ResultCache
from app.repositories.result_cache_repository import result_cache_repository
from app.log import cache_logger as logger
from app.utils.hash import calculate_params_hash


class ResultCacheService:
    """缓存服务类"""

    @staticmethod
    async def get_cache(
            db: AsyncSession,
            *,
            file_hash: str,
            plugin_code: str,
            plugin_version: str,
            params: Optional[Dict[str, Any]] = None
    ) -> Optional[Dict[str, Any]]:
        """
        获取缓存结果

        Args:
            db: 数据库会话
            file_hash: 文件哈希
            plugin_code: 插件编码
            plugin_version: 插件版本
            params: 处理参数

        Returns:
            Optional[Dict[str, Any]]: 缓存结果或None
        """
        try:
            # 计算参数哈希
            params_hash = calculate_params_hash(params)

            # 记录查询参数信息
            logger.debug(f"缓存查询 - 文件: {file_hash}, 插件: {plugin_code}@{plugin_version}")
            logger.debug(f"缓存查询 - 参数: {json.dumps(params, ensure_ascii=False)[:500] if params else None}...")
            logger.debug(f"缓存查询 - 参数哈希: {params_hash}")

            # 查询缓存
            cache = await result_cache_repository.get_by_composite_key(
                db,
                file_hash=file_hash,
                plugin_code=plugin_code,
                plugin_version=plugin_version,
                params_hash=params_hash
            )

            if not cache:
                logger.debug(f"缓存未命中 - 参数哈希: {params_hash}")
                return None

            logger.debug(f"缓存命中 - ID: {cache.id}")

            return {
                "result": cache.result,
                "process_time": cache.process_time
            }

        except Exception as e:
            logger.error(f"获取缓存失败: {str(e)}")
            return None

    @staticmethod
    async def save_cache(
            db: AsyncSession,
            *,
            file_hash: str,
            plugin_code: str,
            plugin_version: str,
            result: Dict[str, Any],
            process_time: Dict[str, Any],
            params: Optional[Dict[str, Any]] = None
    ) -> ResultCache:
        """
        保存缓存结果

        Args:
            db: 数据库会话
            file_hash: 文件哈希
            plugin_code: 插件编码
            plugin_version: 插件版本
            result: 推理结果
            process_time: 处理时间
            params: 处理参数

        Returns:
            ResultCache: 缓存对象

        Raises:
            HTTPException: 保存失败时抛出
        """
        try:
            # 计算参数哈希
            params_hash = calculate_params_hash(params)

            # 记录保存参数信息
            logger.debug(f"缓存保存 - 文件: {file_hash}, 插件: {plugin_code}@{plugin_version}")
            logger.debug(f"缓存保存 - 参数: {json.dumps(params, ensure_ascii=False)[:500] if params else None}...")
            logger.debug(f"缓存保存 - 参数哈希: {params_hash}")

            # 检查是否已存在
            existing_cache = await result_cache_repository.get_by_composite_key(
                db,
                file_hash=file_hash,
                plugin_code=plugin_code,
                plugin_version=plugin_version,
                params_hash=params_hash
            )

            if existing_cache:
                # 更新现有缓存
                logger.debug(f"更新现有缓存 - ID: {existing_cache.id}")
                updated_cache = await result_cache_repository.update(
                    db,
                    db_obj=existing_cache,
                    obj_in={
                        "result": result,
                        "process_time": process_time
                    }
                )
                return updated_cache

            # 创建新缓存
            logger.debug(f"创建新缓存 - 参数哈希: {params_hash}")
            cache_data = {
                "file_hash": file_hash,
                "plugin_code": plugin_code,
                "plugin_version": plugin_version,
                "params_hash": params_hash,
                "result": result,
                "process_time": process_time
            }

            # 使用CRUD基类的create方法，自动处理ID生成
            new_cache = await result_cache_repository.create(db, obj_in=cache_data)
            return new_cache

        except Exception as e:
            logger.error(f"保存缓存失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"保存缓存失败: {str(e)}"
            )

    @staticmethod
    async def delete_cache(
            db: AsyncSession,
            *,
            file_hash: str,
            plugin_code: str,
            plugin_version: str,
            params: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        删除缓存

        Args:
            db: 数据库会话
            file_hash: 文件哈希
            plugin_code: 插件编码
            plugin_version: 插件版本
            params: 处理参数

        Returns:
            bool: 删除是否成功
        """
        try:
            # 计算参数哈希
            params_hash = calculate_params_hash(params)

            # 记录删除参数信息
            logger.debug(f"缓存删除 - 参数哈希: {params_hash}")

            cache = await result_cache_repository.get_by_composite_key(
                db,
                file_hash=file_hash,
                plugin_code=plugin_code,
                plugin_version=plugin_version,
                params_hash=params_hash
            )

            if not cache:
                logger.debug(f"缓存不存在，无法删除 - 参数哈希: {params_hash}")
                return False

            logger.debug(f"删除缓存 - ID: {cache.id}")
            await result_cache_repository.delete(db, id=cache.id)
            return True

        except Exception as e:
            logger.error(f"删除缓存失败: {str(e)}")
            return False

    async def clear_plugin_cache(
        self,
        db: AsyncSession,
        *,
        plugin_code: str,
        plugin_version: Optional[str] = None
    ) -> int:
        """
        清除插件相关的所有缓存

        Args:
            db: 数据库会话
            plugin_code: 插件编码
            plugin_version: 插件版本（可选，不指定则清除所有版本）

        Returns:
            int: 清除的缓存数量
        """
        try:
            # 构建过滤条件
            filters = {"plugin_code": plugin_code}
            if plugin_version:
                filters["plugin_version"] = plugin_version

            # 获取符合条件的缓存
            caches = await result_cache_repository.get_multi(db, filters=filters)

            # 删除缓存
            deleted_count = 0
            for cache_obj in caches:
                await result_cache_repository.delete(db, id=cache_obj.id)
                deleted_count += 1

            logger.info(f"插件缓存清除成功: {plugin_code}:{plugin_version or 'all'}, 清除数量: {deleted_count}")
            return deleted_count

        except Exception as e:
            logger.error(f"清除插件缓存失败: {str(e)}")
            return 0

    async def get_cache_stats(self, db: AsyncSession) -> Dict[str, Any]:
        """
        获取缓存统计信息

        Args:
            db: 数据库会话

        Returns:
            Dict[str, Any]: 缓存统计信息
        """
        try:
            # 获取总缓存数量
            total_count = await result_cache_repository.count(db)

            # 可以添加更多统计信息，如按插件分组的缓存数量等
            stats = {
                "total_count": total_count,
                "status": "active"
            }

            return stats

        except Exception as e:
            logger.error(f"获取缓存统计失败: {str(e)}")
            return {
                "total_count": 0,
                "status": "error",
                "error": str(e)
            }


# 创建全局Service实例
result_cache_service = ResultCacheService()
