aiofiles==24.1.0
aioredis==2.0.1
aliyun-python-sdk-core==2.16.0
aliyun-python-sdk-kms==2.16.5
aliyun-python-sdk-sts==3.1.2
annotated-types==0.7.0
anyio==4.9.0
async-timeout==5.0.1
asyncpg==0.30.0
canonicaljson==2.0.0
certifi==2025.4.26
cffi==1.17.1
charset-normalizer==3.4.2
click==8.2.0
coloredlogs==15.0.1
crcmod==1.7
cryptography==45.0.1
dnspython==2.7.0
email_validator==2.2.0
ExifRead==3.3.1
fastapi==0.115.12
flatbuffers==25.2.10
greenlet==3.2.2
h11==0.16.0
httpcore==1.0.9
httptools==0.6.4
httpx==0.28.1
humanfriendly==10.0
idna==3.10
jmespath==0.10.0
loguru==0.7.3
mpmath==1.3.0
numpy==2.2.6
onnxruntime==1.22.0
opencv-contrib-python==*********
oss2==2.19.1
packaging==25.0
pillow==11.2.1
protobuf==6.31.0
pycparser==2.22
pycryptodome==3.23.0
pydantic==2.11.4
pydantic-settings==2.9.1
pydantic_core==2.33.2
pyexiv2==2.15.4
python-dotenv==1.1.0
python-magic==0.4.27
python-magic-bin==0.4.14
python-multipart==0.0.20
PyYAML==6.0.2
redis==6.1.0
requests==2.32.3
six==1.17.0
sniffio==1.3.1
socksio==1.0.0
SQLAlchemy==2.0.41
starlette==0.46.2
sympy==1.14.0
tenacity==9.1.2
typing-inspection==0.4.0
typing_extensions==4.13.2
ujson==5.10.0
urllib3==2.4.0
uvicorn==0.34.2
uvloop==0.21.0
watchfiles==1.0.5
websockets==15.0.1

# Celery for distributed task queue
celery[redis]==5.4.0
flower==2.0.1

# OpenTelemetry for OpenObserve integration
opentelemetry-api==1.27.0
opentelemetry-sdk==1.27.0
opentelemetry-exporter-otlp==1.27.0
opentelemetry-instrumentation-logging==0.48b0
opentelemetry-instrumentation-fastapi==0.48b0
opentelemetry-instrumentation-requests==0.48b0
opentelemetry-instrumentation-sqlalchemy==0.48b0
opentelemetry-instrumentation-redis==0.48b0
