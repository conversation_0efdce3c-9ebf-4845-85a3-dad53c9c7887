-- 插件表：存储插件元数据和文件路径信息
CREATE TABLE plugin
(
    id               BIGINT PRIMARY KEY,                                    -- 雪花ID，插件唯一标识
    plugin_code      VARCHAR(100) NOT NULL,                                 -- 插件唯一编码
    plugin_version   VARCHAR(20)  NOT NULL,                                 -- 插件版本号
    name             VARCHAR(100) NOT NULL,                                 -- 插件名称
    description      TEXT,                                                  -- 插件描述
    type             VARCHAR(50)  NOT NULL,                                 -- 插件类型，如quantification/detection/mosaic
    engine           VARCHAR(50)  NOT NULL,                                 -- 推理引擎类型，如onnx/pytorch/opencv/api
    input_file_type  JSONB        NOT NULL,                                 -- 支持的输入文件类型
    input_media_type JSONB        NOT NULL,                                 -- 支持的输入媒体类型
    classes          JSONB        NOT NULL,                                 -- 支持的类别列表
    author           VARCHAR(100),                                          -- 作者
    status           VARCHAR(20)  NOT NULL DEFAULT 'enabled',               -- 状态
    model_file_path  TEXT         NOT NULL,                                 -- 模型文件路径
    config_file_path TEXT         NOT NULL,                                 -- 配置文件路径
    python_file_path TEXT         NOT NULL,                                 -- Python处理逻辑文件路径
    is_deleted       BOOLEAN      NOT NULL DEFAULT false,                   -- 逻辑删除标记
    created_at       TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    updated_at       TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- 更新时间
    UNIQUE (plugin_code, plugin_version, is_deleted)
);
-- 索引
CREATE INDEX idx_plugin_status ON plugin (status);
CREATE INDEX idx_plugin_is_deleted ON plugin (is_deleted);

-- 字段注释
COMMENT
ON TABLE plugin IS '插件表，存储插件元数据和文件路径信息';
COMMENT
ON COLUMN plugin.id IS '雪花ID，插件唯一标识';
COMMENT
ON COLUMN plugin.plugin_code IS '插件唯一编码';
COMMENT
ON COLUMN plugin.plugin_version IS '插件版本号';
COMMENT
ON COLUMN plugin.name IS '插件名称';
COMMENT
ON COLUMN plugin.description IS '插件描述';
COMMENT
ON COLUMN plugin.type IS '插件类型，如quantification/detection/mosaic';
COMMENT
ON COLUMN plugin.engine IS '推理引擎类型，如onnx/pytorch/opencv/api';
COMMENT
ON COLUMN plugin.input_file_type IS '支持的输入文件类型';
COMMENT
ON COLUMN plugin.input_media_type IS '支持的输入媒体类型';
COMMENT
ON COLUMN plugin.classes IS '支持的类别列表';
COMMENT
ON COLUMN plugin.author IS '作者';
COMMENT
ON COLUMN plugin.status IS '状态';
COMMENT
ON COLUMN plugin.model_file_path IS '模型文件路径';
COMMENT
ON COLUMN plugin.config_file_path IS '配置文件路径';
COMMENT
ON COLUMN plugin.python_file_path IS 'Python处理逻辑文件路径';
COMMENT
ON COLUMN plugin.is_deleted IS '逻辑删除标记';
COMMENT
ON COLUMN plugin.created_at IS '创建时间';
COMMENT
ON COLUMN plugin.updated_at IS '更新时间';

-- 文件表：存储文件元数据和存储路径
CREATE TABLE file
(
    id            BIGINT PRIMARY KEY,                                    -- 雪花ID，文件唯一标识
    url           TEXT         NOT NULL,                                 -- 文件唯一访问URL
    size          BIGINT       NOT NULL,                                 -- 文件大小(字节)
    file_name     VARCHAR(255) NOT NULL,                                 -- 原始文件名
    oss_path      TEXT         NOT NULL,                                 -- OSS存储路径
    local_path    TEXT,                                                  -- 本地存储路径
    content_type  VARCHAR(100) NOT NULL,                                 -- MIME类型
    file_type     VARCHAR(20)  NOT NULL,                                 -- 文件类型
    media_type    VARCHAR(50)  NOT NULL,                                 -- 媒体类型
    file_hash     VARCHAR(64)  NOT NULL UNIQUE,                          -- SHA256哈希值
    file_metadata JSONB,                                                 -- 文件元数据
    platform      VARCHAR(50),                                           -- 文件来源平台
    is_deleted    BOOLEAN      NOT NULL DEFAULT false,                   -- 逻辑删除标记
    created_at    TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    updated_at    TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- 更新时间
    UNIQUE (file_hash, is_deleted)
);
-- 索引
CREATE INDEX idx_file_url ON file (url);
CREATE INDEX idx_file_is_deleted ON file (is_deleted);


COMMENT
ON TABLE file IS '文件表，存储文件元数据和存储路径';
COMMENT
ON COLUMN file.id IS '雪花ID，文件唯一标识';
COMMENT
ON COLUMN file.url IS '文件唯一访问URL';
COMMENT
ON COLUMN file.size IS '文件大小(字节)';
COMMENT
ON COLUMN file.file_name IS '原始文件名';
COMMENT
ON COLUMN file.oss_path IS 'OSS存储路径';
COMMENT
ON COLUMN file.local_path IS '本地存储路径';
COMMENT
ON COLUMN file.content_type IS 'MIME类型';
COMMENT
ON COLUMN file.file_type IS '文件类型';
COMMENT
ON COLUMN file.media_type IS '媒体类型';
COMMENT
ON COLUMN file.file_hash IS 'SHA256哈希值';
COMMENT
ON COLUMN file.file_metadata IS '文件元数据';
COMMENT
ON COLUMN file.platform IS '文件来源平台';
COMMENT
ON COLUMN file.is_deleted IS '逻辑删除标记';
COMMENT
ON COLUMN file.created_at IS '创建时间';
COMMENT
ON COLUMN file.updated_at IS '更新时间';

-- 任务表：存储任务基本信息和状态
CREATE TABLE task
(
    id              BIGINT PRIMARY KEY,                                    -- 雪花ID，任务唯一标识
    completed_items INTEGER     NOT NULL DEFAULT 0,                        -- 已完成的任务项数量
    total_items     INTEGER     NOT NULL DEFAULT 0,                        -- 总任务项数量
    status          VARCHAR(20) NOT NULL DEFAULT 'pending',                -- 任务状态
    priority        INTEGER     NOT NULL DEFAULT 1,                        -- 优先级
    created_at      TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    updated_at      TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- 更新时间
    started_at      TIMESTAMP WITHOUT TIME ZONE,                           -- 启动时间
    completed_at    TIMESTAMP WITHOUT TIME ZONE                            -- 完成时间
);
-- 索引
CREATE INDEX idx_task_status ON task (status);
CREATE INDEX idx_task_priority ON task (priority);

COMMENT
ON TABLE task IS '任务表，存储任务基本信息和状态';
COMMENT
ON COLUMN task.id IS '雪花ID，任务唯一标识';
COMMENT
ON COLUMN task.completed_items IS '已完成的任务项数量';
COMMENT
ON COLUMN task.total_items IS '总任务项数量';
COMMENT
ON COLUMN task.status IS '任务状态';
COMMENT
ON COLUMN task.priority IS '优先级';
COMMENT
ON COLUMN task.created_at IS '创建时间';
COMMENT
ON COLUMN task.updated_at IS '更新时间';
COMMENT
ON COLUMN task.started_at IS '启动时间';
COMMENT
ON COLUMN task.completed_at IS '完成时间';

-- 任务项表：存储任务项详细信息和处理结果
CREATE TABLE task_item
(
    id             BIGINT PRIMARY KEY,                                    -- 雪花ID，任务项唯一标识
    task_id        BIGINT       NOT NULL,                                 -- 关联任务ID
    data_id        VARCHAR(100),                                          -- 外部系统数据ID
    file_type      VARCHAR(20)  NOT NULL,                                 -- 文件类型
    media_type     VARCHAR(50)  NOT NULL,                                 -- 媒体类型
    file_id        BIGINT       NOT NULL,                                 -- 关联文件ID
    file_url       TEXT         NOT NULL,                                 -- 文件URL
    status         VARCHAR(20)  NOT NULL DEFAULT 'pending',               -- 任务项状态
    error          TEXT,                                                  -- 错误信息
    plugin_code    VARCHAR(100) NOT NULL,                                 -- 插件编码
    plugin_version VARCHAR(20)  NOT NULL,                                 -- 插件版本
    result         JSONB,                                                 -- 推理结果
    params         JSONB,                                                 -- 任务项参数
    from_cache     BOOLEAN      NOT NULL DEFAULT false,                   -- 是否来自缓存
    process_time   JSONB,                                                 -- 处理各阶段时间
    created_at     TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    updated_at     TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- 更新时间
    started_at     TIMESTAMP WITHOUT TIME ZONE,                           -- 启动时间
    completed_at   TIMESTAMP WITHOUT TIME ZONE                            -- 完成时间
);
-- 索引
CREATE INDEX idx_task_item_task_id ON task_item (task_id);
CREATE INDEX idx_task_item_file_id ON task_item (file_id);
CREATE INDEX idx_task_item_plugin ON task_item (plugin_code, plugin_version);
CREATE INDEX idx_task_item_status ON task_item (status);
-- 创建分页查询优化索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_task_item_pagination
    ON task_item (task_id, id);

-- 添加注释说明
COMMENT ON INDEX idx_task_item_pagination IS '任务项分页查询优化索引，确保排序稳定性';

COMMENT
ON TABLE task_item IS '任务项表，存储任务项详细信息和处理结果';
COMMENT
ON COLUMN task_item.id IS '雪花ID，任务项唯一标识';
COMMENT
ON COLUMN task_item.task_id IS '关联任务ID';
COMMENT
ON COLUMN task_item.data_id IS '外部系统数据ID';
COMMENT
ON COLUMN task_item.file_type IS '文件类型';
COMMENT
ON COLUMN task_item.media_type IS '媒体类型';
COMMENT
ON COLUMN task_item.file_id IS '关联文件ID';
COMMENT
ON COLUMN task_item.file_url IS '文件URL';
COMMENT
ON COLUMN task_item.status IS '任务项状态';
COMMENT
ON COLUMN task_item.error IS '错误信息';
COMMENT
ON COLUMN task_item.plugin_code IS '插件编码';
COMMENT
ON COLUMN task_item.plugin_version IS '插件版本';
COMMENT
ON COLUMN task_item.result IS '推理结果';
COMMENT
ON COLUMN task_item.params IS '任务项参数';
COMMENT
ON COLUMN task_item.from_cache IS '是否来自缓存';
COMMENT
ON COLUMN task_item.process_time IS '处理各阶段时间';
COMMENT
ON COLUMN task_item.started_at IS '启动时间';
COMMENT
ON COLUMN task_item.completed_at IS '完成时间';

-- 结果缓存表：存储推理结果缓存
CREATE TABLE result_cache
(
    id             BIGINT PRIMARY KEY,                                    -- 雪花ID，缓存唯一标识
    file_hash      VARCHAR(64)  NOT NULL,                                 -- 文件哈希
    plugin_code    VARCHAR(100) NOT NULL,                                 -- 插件编码
    plugin_version VARCHAR(20)  NOT NULL,                                 -- 插件版本
    params_hash    VARCHAR(64)  NOT NULL,                                 -- 参数哈希
    result         JSONB        NOT NULL,                                 -- 缓存结果
    process_time   JSONB,                                                 -- 处理各阶段时间
    created_at     TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    updated_at     TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- 更新时间
    UNIQUE (file_hash, plugin_code, plugin_version, params_hash)
);
-- 索引
CREATE UNIQUE INDEX idx_result_cache_composite ON result_cache (file_hash, plugin_code, plugin_version, params_hash);

COMMENT
ON TABLE result_cache IS '结果缓存表，存储推理结果缓存';
COMMENT
ON COLUMN result_cache.id IS '雪花ID，缓存唯一标识';
COMMENT
ON COLUMN result_cache.file_hash IS '文件哈希';
COMMENT
ON COLUMN result_cache.plugin_code IS '插件编码';
COMMENT
ON COLUMN result_cache.plugin_version IS '插件版本';
COMMENT
ON COLUMN result_cache.params_hash IS '参数哈希';
COMMENT
ON COLUMN result_cache.result IS '缓存结果';
COMMENT
ON COLUMN result_cache.process_time IS '处理各阶段时间';
COMMENT
ON COLUMN result_cache.use_count IS '缓存使用次数';
COMMENT
ON COLUMN result_cache.created_at IS '创建时间';
COMMENT
ON COLUMN result_cache.updated_at IS '更新时间';