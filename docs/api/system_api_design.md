# 系统管理API设计

## 统一响应格式

所有API响应将遵循以下统一格式：

```json
{
  "code": 0,              // 状态码，0表示成功，其他表示错误
  "success": true,        // 是否成功
  "message": "操作成功",   // 操作结果描述
  "data": {               // 响应数据，可以是对象、数组或null
    // 具体数据内容
  },
  "traceId": "805386ec-4318-4482-8192-2c92dc92eca0", // 请求追踪ID
  "timestamp": "1744184311000" // 响应时间戳（毫秒）
}
```

错误响应示例：

```json
{
  "code": 20001,
  "success": false,
  "message": "系统资源不足",
  "data": null,
  "traceId": "805386ec-4318-4482-8192-2c92dc92eca0",
  "timestamp": "1744184311000"
}
```

## 1. 系统状态概览

获取详细的系统基本信息，包括CPU、内存、GPU、磁盘和网络等信息。

### 请求
```bash
curl -X GET "http://localhost:8000/api/v1/system/os"
```

### 响应
```json
{
  "code": 0,
  "success": true,
  "message": "获取资源使用情况成功",
  "data": {
    "uptime": "3d 4h 12m",
    "cpu": {
      "cores": 32,
      "model": "i5-8200U",
      "utilization": 38.5,
      "temperature": 56.2
    },
    "memory": {
      "total": 128,
      "used": 78.4,
      "utilization": 61.3
    },
    "gpu": [
      {
        "id": 0,
        "model": "NVIDIA RTX A6000",
        "totalMemory": 48,
        "usedMemory": 32.6,
        "utilization": 68.2,
        "temperature": 65.3
      },
      {
        "id": 1,
        "model": "NVIDIA RTX A6000",
        "totalMemory": 48,
        "usedMemory": 28.4,
        "utilization": 59.2,
        "temperature": 62.8
      }
    ],
    "disk": {
      "total": 2048,
      "used": 1242.7,
      "utilization": 60.7
    }
  },
  "traceId": "805386ec-4318-4482-8192-2c92dc92eca0",
  "timestamp": "1744184311000"
}
```
