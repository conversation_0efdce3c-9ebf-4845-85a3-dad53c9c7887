# 任务管理API设计

## 统一响应格式

所有API响应将遵循以下统一格式：

```json
{
  "code": 0,              // 状态码，0表示成功，其他表示错误
  "success": true,        // 是否成功
  "message": "操作成功",   // 操作结果描述
  "data": {               // 响应数据，可以是对象、数组或null
    // 具体数据内容
  },
  "traceId": "805386ec-4318-4482-8192-2c92dc92eca0", // 请求追踪ID
  "timestamp": "1744184311000" // 响应时间戳（毫秒）
}
```

错误响应示例：

```json
{
  "code": 30001,
  "success": false,
  "message": "任务不存在",
  "data": null,
  "traceId": "805386ec-4318-4482-8192-2c92dc92eca0",
  "timestamp": "1744184311000"
}
```

## 1. 提交任务

创建一个新的处理任务，可以包含多个数据项（图片或视频）。

### 请求
```bash
curl -X POST "http://localhost:8000/api/v1/tasks" \
  -H "Content-Type: application/json" \
  -d '{
    "priority": 1,
    "plugins": [
        {
            "pluginCode": "glass_panel_crack",
            "pluginVersion": "1.0.0"
        }
    ],
    "items": [
        {
            "dataId": "75",
            "fileType": "image",
            "fileUrl": "https://insky-oss.oss-cn-shanghai.aliyuncs.com/tests/insky20210625_0032.jpg",
            "mediaType": "visible_light",
            "params": {
              "iussues": "xxx"
            }
        },
         {
            "dataId": "76",
            "fileType": "image",
            "fileUrl": "https://insky-oss.oss-cn-shanghai.aliyuncs.com/tests/insky_20210625_0027.jpg",
            "mediaType": "visible_light",
            "params": {}
        }
    ]
}'
```

### 响应
```json
{
  "code": 0,
  "success": true,
  "message": "任务提交成功",
  "data": {
    "taskId": 12345,
    "progress": "0/2",
    "status": "pending",
    "priority": 1,
    "createdAt": "2023-12-01 14:30:00"
  },
  "traceId": "805386ec-4318-4482-8192-2c92dc92eca0",
  "timestamp": "1744184311000"
}
```

## 2. 获取任务列表

获取所有任务或按条件筛选的任务列表。

### 请求
```bash
# 获取所有任务
curl -X GET "http://localhost:8000/api/v1/tasks"

# 带过滤条件的查询
curl -X GET "http://localhost:8000/api/v1/tasks?status=processing&priority=7&page=1&pageSize=10"
```

### 响应
```json
{
  "code": 0,
  "success": true,
  "message": "获取任务列表成功",
  "data": {
    "totalCount": 5,
    "totalPage": 1,
    "page": 1,
    "pageSize": 10,
    "items": [
      {
        "taskId": 12340,
        "progress": "0/2",
        "status": "pending",
        "priority": 1,
        "createdAt": "2023-12-01 14:30:00",
        "updatedAt": "2023-12-01 14:30:22"
      },
      {
        "taskId": 12341,
        "progress": "1/2",
        "status": "running",
        "priority": 7,
        "createdAt": "2023-12-01 14:30:00",
        "updatedAt": "2023-12-01 14:30:22",
        "startedAt": "2025-05-16 11:39:26"
      }
      {
        "taskId": 12342,
        "progress": "1/2",
        "status": "failed",
        "priority": 1,
        "createdAt": "2023-12-01 14:30:00",
        "updatedAt": "2023-12-01 14:30:22",
        "startedAt": "2025-05-16 11:39:26",
        "completedAt": "2025-05-16 11:39:26"
      },
      {
        "taskId": 12343,
        "progress": "2/2",
        "status": "completed",
        "priority": 1,
        "createdAt": "2023-12-01 14:30:00",
        "updatedAt": "2023-12-01 14:30:22",
        "startedAt": "2025-05-16 11:39:26",
        "completedAt": "2025-05-16 11:39:26"
      },
      {
        "taskId": 12344,
        "progress": "1/2",
        "status": "canceled",
        "priority": 1,
        "createdAt": "2023-12-01 14:30:00",
        "updatedAt": "2023-12-01 14:30:22",
        "startedAt": "2025-05-16 11:39:26",
        "completedAt": "2025-05-16 11:39:26"
      }
    ]
  },
  "traceId": "805386ec-4318-4482-8192-2c92dc92eca0",
  "timestamp": "1744184311000"
}
```

## 3. 获取任务详情

获取单个任务的详细信息。

### 请求
```bash
curl -X GET "http://localhost:8000/api/v1/tasks/12345"
```

### 响应
```json
{
  "code": 0,
  "success": true,
  "message": "获取任务详情成功",
  "data":  {
    "taskId": 12341,
    "progress": "1/2",
    "status": "running",
    "priority": 7,
    "createdAt": "2023-12-01 14:30:00",
    "updatedAt": "2023-12-01 14:30:22",
    "startedAt": "2025-05-16 11:39:26"
  },
  "traceId": "805386ec-4318-4482-8192-2c92dc92eca0",
  "timestamp": "1744184311000"
}
```

## 4. 取消任务

取消一个正在进行的任务。

### 请求
```bash
curl -X GET "http://localhost:8000/api/v1/tasks/12345/cancel"
```

### 响应
```json
{
  "code": 0,
  "success": true,
  "message": "任务取消成功",
  "data": {
    "taskId": 12345,
    "status": "canceled",
    "progress": "1/2",
    "createdAt": "2023-12-01 14:30:00",
    "updatedAt": "2023-12-01 14:30:22",
    "startedAt": "2025-05-16 11:39:26",
    "completedAt": "2025-05-16 11:39:26"
  },
  "traceId": "805386ec-4318-4482-8192-2c92dc92eca0",
  "timestamp": "1744184311000"
}
```

## 5. 调整任务优先级

修改任务的优先级，影响其在队列中的处理顺序。

### 请求
```bash
curl -X PUT "http://localhost:8000/api/v1/tasks/12345/priority" \
  -H "Content-Type: application/json" \
  -d '{
    "priority": 9
  }'
```

### 响应
```json
{
  "code": 0,
  "success": true,
  "message": "任务优先级调整成功",
  "data": {
    "taskId": 12345,
    "priority": 9,
    "status": "pending",
    "progress": "0/2",
    "createdAt": "2023-12-01 14:30:00",
    "updatedAt": "2023-12-01 14:30:22"
  },
  "traceId": "805386ec-4318-4482-8192-2c92dc92eca0",
  "timestamp": "1744184311000"
}
```

## 6. 获取任务的任务项

获取任务的任务项

### 请求
```bash
curl -X GET "http://localhost:8000/api/v1/tasks/12345/items?page=1&pageSize=10"
```

### 响应
```json
{
    "code": 0,
    "success": true,
    "message": "获取任务项列表成功",
    "data": {
        "totalCount": 2,
        "totalPage": 1,
        "page": 1,
        "pageSize": 10,
        "items": [
            {
                "taskId": 313845808187310080,
                "taskItemId": 313845808296361985,
                "dataId": "76",
                "fileType": "image",
                "mediaType": "visible_light",
                "fileHash": "c7b65bff5768398c639f33b7ec73ce02b9ca399a5a2f7035c2d0ac14fd71041b",
                "fileUrl": "https://insky-oss.oss-cn-shanghai.aliyuncs.com/tests/insky_20210625_0027.jpg",
                "status": "completed",
                "error": null,
                "pluginCode": "glass_panel_crack",
                "pluginVersion": "1.0.0",
                "result": [
                    {
                        "code": "issueType-001-001-001",
                        "points": [
                            [
                                778.6384887695312,
                                4.064422607421875
                            ],
                            [
                                1080.0,
                                4.064422607421875
                            ],
                            [
                                1080.0,
                                491.69207763671875
                            ],
                            [
                                778.6384887695312,
                                491.69207763671875
                            ]
                        ],
                        "shapeType": "rectangle",
                        "score": 0.9211642742156982
                    },
                    {
                        "code": "issueType-001-001-001",
                        "points": [
                            [
                                4.506439208984375,
                                1011.7822265625
                            ],
                            [
                                404.17620849609375,
                                1011.7822265625
                            ],
                            [
                                404.17620849609375,
                                1272.694091796875
                            ],
                            [
                                4.506439208984375,
                                1272.694091796875
                            ]
                        ],
                        "shapeType": "rectangle",
                        "score": 0.8709656596183777
                    },
                    {
                        "code": "issueType-001-001-001",
                        "points": [
                            [
                                247.3106689453125,
                                519.0302734375
                            ],
                            [
                                732.8629150390625,
                                519.0302734375
                            ],
                            [
                                732.8629150390625,
                                994.1949462890625
                            ],
                            [
                                247.3106689453125,
                                994.1949462890625
                            ]
                        ],
                        "shapeType": "rectangle",
                        "score": 0.8532591462135315
                    }
                ],
                "startedAt": "2025-05-16 01:11:12",
                "completedAt": "2025-05-16 01:11:12"
            }
        ]
    },
    "traceId": "805386ec-4318-4482-8192-2c92dc92eca0",
    "timestamp": "1744184311000"
}
```