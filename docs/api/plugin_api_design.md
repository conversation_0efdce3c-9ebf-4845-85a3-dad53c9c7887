# 插件管理API设计

## 统一响应格式

所有API响应将遵循以下统一格式：

```json
{
  "code": 0,              // 状态码，0表示成功，其他表示错误
  "success": true,        // 是否成功
  "message": "操作成功",   // 操作结果描述
  "data": {               // 响应数据，可以是对象、数组或null
    // 具体数据内容
  },
  "traceId": "805386ec-4318-4482-8192-2c92dc92eca0", // 请求追踪ID
  "timestamp": "1744184311000" // 响应时间戳（毫秒）
}
```

错误响应示例：

```json
{
  "code": 10001,
  "success": false,
  "message": "插件代码已存在",
  "data": null,
  "traceId": "805386ec-4318-4482-8192-2c92dc92eca0",
  "timestamp": "1744184311000"
}
```

## 1. 注册新插件

### 请求
```bash
curl -X POST "http://localhost:8000/api/v1/plugins" \
  -H "Content-Type: multipart/form-data" \
  -F "pluginModelFile=@/path/to/glass_panel_crack.onnx" \
  -F "pluginConfigFile=@/path/to/glass_panel_crack.yaml" \
  -F "pluginPythonFile=@/path/to/glass_panel_crack.py" 
```

### 响应
```json
{
    "code": 0,
    "success": true,
    "message": "插件注册成功",
    "data": {
        "id": 1,
        "pluginCode": "glass_panel_crack",
        "pluginVersion": "1.0.3",
        "type": "detection",
        "inputFileType": [
            "image",
            "video"
        ],
        "inputMediaType": [
            "visible_light",
            "infrared_light"
        ],
        "classes": [
            "issueType-001-001-001"
        ],
        "name": "玻璃破损检测",
        "description": "基于YOLOv11n的玻璃面板破损检测插件",
        "author": "AI团队",
        "status": "enabled",
        "createdAt": "2023-12-01 10:00:00",
        "updatedAt": "2023-12-01 10:00:00"
    },
    "traceId": "805386ec-4318-4482-8192-2c92dc92eca0",
    "timestamp": "1744184311000"
}
```

## 2. 获取插件列表

### 请求
```bash
# 获取所有插件
curl -X GET "http://localhost:8000/api/v1/plugins"

# 带过滤条件的查询
curl -X GET "http://localhost:8000/api/v1/plugins?pluginCode=glass&name=玻璃&status=enabled&page=1&pageSize=10"
```

### 响应
```json
{
  "code": 0,
  "success": true,
  "message": "获取插件列表成功",
  "data": {
    "totalCount": 2,
    "totalPage": 1,
    "page": 1,
    "pageSize": 10,
    "items": [
      {
        "id": 1,
        "pluginCode": "glass_panel_crack",
        "pluginVersion": "1.0.3",
        "name": "玻璃破损检测",
        "description": "基于YOLOv11n的玻璃面板破损检测插件",
        "type": "detection",
        "inputFileType": [
            "image",
            "video"
        ],
        "inputMediaType": [
            "visible_light",
            "infrared_light"
        ],
        "classes": [
            "issueType-001-001-001"
        ],
        "author": "AI团队",
        "status": "enabled",
        "createdAt": "2023-12-01 10:00:00",
        "updatedAt": "2023-12-01 10:00:00"
      },
      {
        "id": 2,
        "pluginCode": "face_detection",
        "pluginVersion": "1.0.0",
        "name": "人脸检测",
        "description": "基于YOLOv8的人脸检测插件",
        "type": "detection",
        "inputFileType": [
            "image",
            "video"
        ],
        "inputMediaType": [
            "visible_light",
            "infrared_light"
        ],
        "classes": [
            "issueType-001-001-001"
        ],
        "author": "AI团队",
        "status": "enabled",
        "createdAt": "2023-12-01 10:00:00",
        "updatedAt": "2023-12-01 11:00:00"
      }
    ]
  },
  "traceId": "805386ec-4318-4482-8192-2c92dc92eca0",
  "timestamp": "1744184311000"
}
```

## 3. 获取插件详情

### 请求
```bash
curl -X GET "http://localhost:8000/api/v1/plugins/1"
```

### 响应
```json
{
  "code": 0,
  "success": true,
  "message": "获取插件详情成功",
  "data": {
    "id": 1,
    "pluginCode": "glass_panel_crack",
    "pluginVersion": "1.0.3",
    "name": "玻璃破损检测",
    "description": "基于YOLOv11n的玻璃面板破损检测插件",
    "type": "detection",
    "inputFileType": [
        "image",
        "video"
    ],
    "inputMediaType": [
        "visible_light",
        "infrared_light"
    ],
    "classes": [
        "issueType-001-001-001"
    ],
    "author": "AI团队",
    "status": "enabled",
    "createdAt": "2023-12-01 10:00:00",
    "updatedAt": "2023-12-01 10:00:00"
  },
  "traceId": "805386ec-4318-4482-8192-2c92dc92eca0",
  "timestamp": "1744184311000"
}
```

## 4. 修改插件

### 请求
```bash
curl -X PUT "http://localhost:8000/api/v1/plugins/1" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "玻璃破损检测(升级版)",
    "description": "基于YOLOv11n的玻璃面板破损检测插件(升级版)",
    "author": "AI团队-升级组",
    "status": "disabled"
  }'
```

### 响应
```json
{
  "code": 0,
  "success": true,
  "message": "插件更新成功",
  "data": {
    "id": 1,
    "pluginCode": "glass_panel_crack",
    "pluginVersion": "1.0.4",
    "name": "玻璃破损检测(升级版)",
    "description": "基于YOLOv11n的玻璃面板破损检测插件(升级版)",
    "type": "detection",
    "inputFileType": [
        "image",
        "video"
    ],
    "inputMediaType": [
        "visible_light",
        "infrared_light"
    ],
    "classes": [
        "issueType-001-001-001"
    ],
    "author": "AI团队-升级组",
    "status": "disabled",
    "createdAt": "2023-12-01T10:00:00Z",
    "updatedAt": "2023-12-01T11:30:00Z"
  },
  "traceId": "805386ec-4318-4482-8192-2c92dc92eca0",
  "timestamp": "1744184311000"
}
```

## 5. 删除插件

### 请求
```bash
curl -X DELETE "http://localhost:8000/api/v1/plugins/1"
```

### 响应
```json
{
  "code": 0,
  "success": true,
  "message": "插件删除成功",
  "data": null,
  "traceId": "805386ec-4318-4482-8192-2c92dc92eca0",
  "timestamp": "1744184311000"
}
```
