# 文件管理API设计

## 统一响应格式

所有API响应将遵循以下统一格式：

```json
{
  "code": 0,              // 状态码，0表示成功，其他表示错误
  "success": true,        // 是否成功
  "message": "操作成功",   // 操作结果描述
  "data": {               // 响应数据，可以是对象、数组或null
    // 具体数据内容
  },
  "traceId": "805386ec-4318-4482-8192-2c92dc92eca0", // 请求追踪ID
  "timestamp": "1744184311000" // 响应时间戳（毫秒）
}
```

错误响应示例：

```json
{
  "code": 30001,
  "success": false,
  "message": "任务不存在",
  "data": null,
  "traceId": "805386ec-4318-4482-8192-2c92dc92eca0",
  "timestamp": "1744184311000"
}
```

## 1. 上传文件

上传文件并返回文件信息

### 请求
```bash
curl  -X POST 'http://localhost:8000/api/v1/files/upload' \
--form 'file=@/path/to/glass_panel_crack.onnx' \
--form 'fileType=image' \
--form 'mediaType=face' \
--form 'generateMetadata=false'
```

### 响应
```json
{
    "code": 0,
    "success": true,
    "message": "任务提交成功",
    "data": {
        "id": "12345678901234567890",
        "url": "https://example.com/files/image.jpg",
        "size": 1024,
        "filename": "image.jpg",
        "oss_path": "images/abcdef1234567890.jpg",
        "local_path": "/storage/images/abcdef1234567890.jpg",
        "content_type": "image/jpeg",
        "file_type": "image",
        "media_type": "visible_light",
        "file_hash": "abcdef1234567890abcdef1234567890",
        "created_at": "2023-01-01 12:00:00",
        "updated_at": "2023-01-01 12:00:00"
    },
    "traceId": "805386ec-4318-4482-8192-2c92dc92eca0",
    "timestamp": "1744184311000"
}
```

## 2. 获取OSS临时访问URL

生成指定文件在OSS上的临时访问URL

### 请求

```bash
curl -X GET "http://localhost:8000/api/v1/files/1111/sts?expires=3600"
```

### 响应
```json
{
  "code": 0,
  "success": true,
  "message": "获取任务列表成功",
  "data": "https://insky-oss.oss-cn-shanghai.aliyuncs.com/tests/insky_20210625_0027.jpg?1212121212",
  "traceId": "805386ec-4318-4482-8192-2c92dc92eca0",
  "timestamp": "1744184311000"
}
```
