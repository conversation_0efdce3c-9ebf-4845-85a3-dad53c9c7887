#!/usr/bin/env python3
"""
Trace链路完整性测试
验证从HTTP请求到异步任务完成的完整trace链路
"""
import asyncio
import json
import time
import re
from typing import Dict, List, Optional
from datetime import datetime, timedelta

import httpx
import pytest

# 测试配置
TEST_CONFIG = {
    "api_base_url": "http://localhost:8000",
    "test_timeout": 60,  # 测试超时时间（秒）
    "log_check_interval": 2,  # 日志检查间隔（秒）
    "max_log_wait": 30,  # 最大日志等待时间（秒）
}


class TraceTestResult:
    """Trace测试结果"""
    
    def __init__(self):
        self.trace_id: Optional[str] = None
        self.http_logs: List[Dict] = []
        self.task_logs: List[Dict] = []
        self.scheduler_logs: List[Dict] = []
        self.plugin_logs: List[Dict] = []
        self.db_logs: List[Dict] = []
        self.start_time: Optional[datetime] = None
        self.end_time: Optional[datetime] = None
        self.task_id: Optional[int] = None
        
    def add_log(self, log_entry: Dict):
        """添加日志条目"""
        component = log_entry.get("component", "unknown")
        if "http" in component.lower() or "middleware" in component.lower():
            self.http_logs.append(log_entry)
        elif "task_manager" in component.lower():
            self.task_logs.append(log_entry)
        elif "scheduler" in component.lower():
            self.scheduler_logs.append(log_entry)
        elif "plugin" in component.lower():
            self.plugin_logs.append(log_entry)
        elif "sql" in log_entry.get("message", "").lower():
            self.db_logs.append(log_entry)
    
    def get_summary(self) -> Dict:
        """获取测试结果摘要"""
        return {
            "trace_id": self.trace_id,
            "task_id": self.task_id,
            "duration": (self.end_time - self.start_time).total_seconds() if self.start_time and self.end_time else None,
            "log_counts": {
                "http": len(self.http_logs),
                "task": len(self.task_logs),
                "scheduler": len(self.scheduler_logs),
                "plugin": len(self.plugin_logs),
                "database": len(self.db_logs),
                "total": len(self.http_logs) + len(self.task_logs) + len(self.scheduler_logs) + len(self.plugin_logs) + len(self.db_logs)
            },
            "trace_continuity": self.check_trace_continuity()
        }
    
    def check_trace_continuity(self) -> Dict:
        """检查trace连续性"""
        all_logs = self.http_logs + self.task_logs + self.scheduler_logs + self.plugin_logs + self.db_logs
        
        # 检查所有日志是否有相同的trace_id
        trace_ids = set()
        logs_with_trace = 0
        logs_without_trace = 0
        
        for log in all_logs:
            trace_id = log.get("trace_id")
            if trace_id:
                trace_ids.add(trace_id)
                logs_with_trace += 1
            else:
                logs_without_trace += 1
        
        return {
            "unique_trace_ids": len(trace_ids),
            "primary_trace_id": self.trace_id,
            "logs_with_trace": logs_with_trace,
            "logs_without_trace": logs_without_trace,
            "trace_consistency": len(trace_ids) <= 1 and logs_without_trace == 0,
            "coverage_percentage": (logs_with_trace / len(all_logs) * 100) if all_logs else 0
        }


class TraceTestRunner:
    """Trace测试运行器"""
    
    def __init__(self):
        self.client = httpx.AsyncClient(timeout=TEST_CONFIG["test_timeout"])
        self.result = TraceTestResult()
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    async def submit_test_task(self) -> Dict:
        """提交测试任务"""
        test_task = {
            "items": [
                {
                    "file_url": "https://example.com/test-file.txt",
                    "params": {"test": True}
                }
            ],
            "plugins": [
                {
                    "plugin_code": "test_plugin",
                    "plugin_version": "1.0.0"
                }
            ],
            "priority": 1
        }
        
        self.result.start_time = datetime.now()
        
        response = await self.client.post(
            f"{TEST_CONFIG['api_base_url']}/api/v1/tasks/",
            json=test_task
        )
        
        if response.status_code != 200:
            raise Exception(f"任务提交失败: {response.status_code} - {response.text}")
        
        task_response = response.json()
        self.result.task_id = task_response.get("id")
        
        # 从响应头中提取trace_id
        trace_id = response.headers.get("x-trace-id")
        if trace_id:
            self.result.trace_id = trace_id
        
        return task_response
    
    async def wait_for_task_completion(self, task_id: int, timeout: int = 30) -> bool:
        """等待任务完成"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                response = await self.client.get(
                    f"{TEST_CONFIG['api_base_url']}/api/v1/tasks/{task_id}"
                )
                
                if response.status_code == 200:
                    task_data = response.json()
                    status = task_data.get("status")
                    
                    if status in ["completed", "failed", "canceled"]:
                        self.result.end_time = datetime.now()
                        return status == "completed"
                
                await asyncio.sleep(2)
                
            except Exception as e:
                print(f"检查任务状态时出错: {e}")
                await asyncio.sleep(2)
        
        return False
    
    def parse_log_file(self, log_file_path: str = "logs/app.log") -> List[Dict]:
        """解析日志文件"""
        logs = []
        
        try:
            with open(log_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 只处理最近的日志（最后1000行）
            recent_lines = lines[-1000:] if len(lines) > 1000 else lines
            
            for line in recent_lines:
                line = line.strip()
                if not line:
                    continue
                
                # 解析日志行，提取trace_id和其他信息
                log_entry = self.parse_log_line(line)
                if log_entry:
                    logs.append(log_entry)
        
        except FileNotFoundError:
            print(f"日志文件不存在: {log_file_path}")
        except Exception as e:
            print(f"解析日志文件时出错: {e}")
        
        return logs
    
    def parse_log_line(self, line: str) -> Optional[Dict]:
        """解析单行日志"""
        try:
            # 尝试匹配包含trace_id的日志行
            trace_pattern = r'trace_id[=:]([a-f0-9]{32})'
            trace_match = re.search(trace_pattern, line)
            
            if trace_match:
                trace_id = trace_match.group(1)
                
                # 提取时间戳
                timestamp_pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})'
                timestamp_match = re.search(timestamp_pattern, line)
                timestamp = timestamp_match.group(1) if timestamp_match else None
                
                # 提取日志级别
                level_pattern = r'\| (\w+)\s+\|'
                level_match = re.search(level_pattern, line)
                level = level_match.group(1) if level_match else "INFO"
                
                return {
                    "timestamp": timestamp,
                    "level": level,
                    "trace_id": trace_id,
                    "message": line,
                    "component": self.extract_component(line)
                }
        
        except Exception as e:
            print(f"解析日志行时出错: {e}")
        
        return None
    
    def extract_component(self, line: str) -> str:
        """从日志行中提取组件信息"""
        if "task_manager" in line.lower():
            return "task_manager"
        elif "scheduler" in line.lower():
            return "scheduler"
        elif "plugin" in line.lower():
            return "plugin_executor"
        elif "sql" in line.lower():
            return "database"
        elif "http" in line.lower() or "middleware" in line.lower():
            return "http"
        else:
            return "unknown"
    
    async def collect_trace_logs(self, wait_time: int = 10):
        """收集trace相关的日志"""
        # 等待一段时间让所有日志写入
        await asyncio.sleep(wait_time)
        
        logs = self.parse_log_file()
        
        # 过滤与当前trace_id相关的日志
        if self.result.trace_id:
            for log in logs:
                if log.get("trace_id") == self.result.trace_id:
                    self.result.add_log(log)
    
    async def run_full_test(self) -> TraceTestResult:
        """运行完整的trace测试"""
        try:
            print("🚀 开始trace链路完整性测试...")
            
            # 1. 提交测试任务
            print("📝 提交测试任务...")
            task_response = await self.submit_test_task()
            task_id = task_response.get("id")
            print(f"✅ 任务提交成功，ID: {task_id}, Trace ID: {self.result.trace_id}")
            
            # 2. 等待任务完成
            print("⏳ 等待任务完成...")
            completed = await self.wait_for_task_completion(task_id)
            
            if completed:
                print("✅ 任务执行完成")
            else:
                print("⚠️ 任务未在预期时间内完成")
            
            # 3. 收集和分析日志
            print("📊 收集trace日志...")
            await self.collect_trace_logs()
            
            # 4. 生成测试报告
            summary = self.result.get_summary()
            print("📋 测试结果摘要:")
            print(json.dumps(summary, indent=2, ensure_ascii=False))
            
            return self.result
            
        except Exception as e:
            print(f"❌ 测试执行失败: {e}")
            raise


async def main():
    """主测试函数"""
    async with TraceTestRunner() as runner:
        result = await runner.run_full_test()
        
        # 验证测试结果
        continuity = result.check_trace_continuity()
        
        if continuity["trace_consistency"] and continuity["coverage_percentage"] > 80:
            print("🎉 Trace链路完整性测试通过！")
            return True
        else:
            print("❌ Trace链路完整性测试失败！")
            print(f"覆盖率: {continuity['coverage_percentage']:.1f}%")
            print(f"一致性: {continuity['trace_consistency']}")
            return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
