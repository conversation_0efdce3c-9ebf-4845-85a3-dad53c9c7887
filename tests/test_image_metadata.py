#!/usr/bin/env python3
"""
测试图片元数据提取失败的诊断脚本
"""

import os
import sys
import traceback
import asyncio
from pathlib import Path
from fractions import Fraction

def check_dependencies():
    """检查依赖库是否正确安装"""
    print("=== 检查依赖库 ===")
    
    # 检查pyexiv2
    try:
        import pyexiv2
        from pyexiv2 import Image as PyexivImage
        print("✓ pyexiv2 已安装")
        print(f"  版本: {pyexiv2.__version__ if hasattr(pyexiv2, '__version__') else '未知'}")
    except ImportError as e:
        print(f"✗ pyexiv2 未安装或导入失败: {e}")
        return False
    
    # 检查exifread
    try:
        import exifread
        print("✓ exifread 已安装")
        print(f"  版本: {exifread.__version__ if hasattr(exifread, '__version__') else '未知'}")
    except ImportError as e:
        print(f"✗ exifread 未安装或导入失败: {e}")
        return False
    
    # 检查PIL
    try:
        from PIL import Image as PILImage
        print("✓ PIL (Pillow) 已安装")
        print(f"  版本: {PILImage.__version__ if hasattr(PILImage, '__version__') else '未知'}")
    except ImportError as e:
        print(f"✗ PIL (Pillow) 未安装或导入失败: {e}")
        return False
    
    return True

def check_file_basic_info(file_path):
    """检查文件基本信息"""
    print(f"\n=== 检查文件基本信息 ===")
    print(f"文件路径: {file_path}")
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"✗ 文件不存在: {file_path}")
        return False
    
    print("✓ 文件存在")
    
    # 检查文件大小
    file_size = os.path.getsize(file_path)
    print(f"文件大小: {file_size} bytes ({file_size / 1024:.2f} KB)")
    
    # 检查文件权限
    if os.access(file_path, os.R_OK):
        print("✓ 文件可读")
    else:
        print("✗ 文件不可读")
        return False
    
    # 检查文件扩展名
    _, ext = os.path.splitext(file_path)
    print(f"文件扩展名: {ext}")
    
    return True

def test_pil_image(file_path):
    """使用PIL测试图片是否可以正常打开"""
    print(f"\n=== PIL图片测试 ===")
    
    try:
        from PIL import Image as PILImage
        
        with PILImage.open(file_path) as img:
            print(f"✓ PIL可以打开图片")
            print(f"  格式: {img.format}")
            print(f"  模式: {img.mode}")
            print(f"  尺寸: {img.size}")
            
            # 检查是否有EXIF数据
            if hasattr(img, '_getexif') and img._getexif():
                print("✓ 图片包含EXIF数据")
            else:
                print("⚠ 图片不包含EXIF数据或无法读取")
                
        return True
        
    except Exception as e:
        print(f"✗ PIL无法打开图片: {e}")
        return False

def test_pyexiv2_basic(file_path):
    """测试pyexiv2基本功能"""
    print(f"\n=== pyexiv2基本测试 ===")
    
    try:
        import pyexiv2
        from pyexiv2 import Image as PyexivImage
        
        # 尝试创建Image对象
        img = PyexivImage(file_path)
        print("✓ pyexiv2可以创建Image对象")
        
        # 尝试读取EXIF数据
        try:
            dict_exif = img.read_exif()
            print(f"✓ 成功读取EXIF数据，包含 {len(dict_exif)} 个字段")
            
            # 显示前几个EXIF字段
            if dict_exif:
                print("  前5个EXIF字段:")
                for i, (key, value) in enumerate(list(dict_exif.items())[:5]):
                    print(f"    {key}: {value}")
            
        except Exception as e:
            print(f"⚠ 读取EXIF数据失败: {e}")
        
        # 尝试读取XMP数据
        try:
            dict_xmp = img.read_xmp()
            print(f"✓ 成功读取XMP数据，包含 {len(dict_xmp)} 个字段")
            
            # 显示前几个XMP字段
            if dict_xmp:
                print("  前5个XMP字段:")
                for i, (key, value) in enumerate(list(dict_xmp.items())[:5]):
                    print(f"    {key}: {value}")
                    
        except Exception as e:
            print(f"⚠ 读取XMP数据失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ pyexiv2测试失败: {e}")
        print(f"错误详情: {traceback.format_exc()}")
        return False

def test_exifread_basic(file_path):
    """测试exifread基本功能"""
    print(f"\n=== exifread基本测试 ===")
    
    try:
        import exifread
        
        with open(file_path, "rb") as f:
            tags = exifread.process_file(f)
            
        print(f"✓ exifread成功处理文件，找到 {len(tags)} 个标签")
        
        if tags:
            print("  前10个标签:")
            for i, (key, value) in enumerate(list(tags.items())[:10]):
                print(f"    {key}: {value}")
        else:
            print("  ⚠ 未找到任何EXIF标签")
        
        return True
        
    except Exception as e:
        print(f"✗ exifread测试失败: {e}")
        return False

async def test_custom_metadata_extraction(file_path):
    """测试自定义的元数据提取函数（复制项目逻辑）"""
    print(f"\n=== 自定义元数据提取测试 ===")

    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"✗ 文件不存在: {file_path}")
            return None

        # 导入必要的库
        import pyexiv2
        from pyexiv2 import Image as PyexivImage
        import exifread

        # 读取图像元数据
        img = PyexivImage(file_path)

        dict_xmp = img.read_xmp()
        dict_exif = img.read_exif()
        dict_exif['Exif.Photo.MakerNote'] = None

        dict_exif.update(dict_xmp)

        # 转换DMS到十进制
        def dms_to_decimal(dms_str):
            """将度分秒格式的字符串转换为十进制格式"""
            if dms_str is None:
                return None
            try:
                # 分割度、分、秒
                parts = dms_str.split()
                if len(parts) != 3:
                    raise ValueError(f"Invalid DMS format: {dms_str}")

                degrees = Fraction(parts[0].split('/')[0]) / Fraction(parts[0].split('/')[1]) if '/' in parts[0] else float(parts[0])
                minutes = Fraction(parts[1].split('/')[0]) / Fraction(parts[1].split('/')[1]) if '/' in parts[1] else float(parts[1])
                seconds = Fraction(parts[2].split('/')[0]) / Fraction(parts[2].split('/')[1]) if '/' in parts[2] else float(parts[2])

                decimal = degrees + (minutes / 60.0) + (seconds / 3600.0)
                return float(decimal)
            except Exception as e:
                print(f"转换DMS错误: {dms_str}, 错误: {e}")
                return dms_str

        def convert_to_decimal(value):
            if value is None:
                return None
            if '/' in value:
                upper, down = value.split('/')
                res = float(upper) / float(down)
            else:
                res = float(value)
            return str(res)

        with open(file_path, "rb") as f:
            tags = exifread.process_file(f)

            # 定义最终要返回的字段及其处理方式
            imageWidth_get=dict_exif.get("Exif.Photo.PixelXDimension", None)
            imageWidth=imageWidth_get+" pixels" if imageWidth_get else None

            focalLength35_get = dict_exif.get("Exif.Photo.FocalLengthIn35mmFilm", None)
            focalLength35=focalLength35_get+" mm" if focalLength35_get else None

            exifImageWidth_get = dict_exif.get("Exif.Photo.PixelXDimension", None)
            exifImageWidth=exifImageWidth_get+" pixels" if exifImageWidth_get else None

            exifImageHeight_get = dict_exif.get("Exif.Photo.PixelYDimension", None)
            exifImageHeight=exifImageHeight_get+" pixels" if exifImageHeight_get else None

            imageHeight_get = dict_exif.get("Exif.Photo.PixelYDimension", None)
            imageHeight=imageHeight_get+" pixels" if imageHeight_get else None

            focalLength_get = dict_exif.get("Exif.Photo.FocalLength", None)
            focalLength=convert_to_decimal(focalLength_get) +" mm" if focalLength_get else None

            gpsAltitude_get = dict_exif.get("Exif.GPSInfo.GPSAltitude", None)
            gpsAltitude=convert_to_decimal(gpsAltitude_get) +" metres" if gpsAltitude_get else None

            gpsLatitude_get = dict_exif.get("Exif.GPSInfo.GPSLatitude", None)
            gpsLatitude=dms_to_decimal(gpsLatitude_get) if gpsLatitude_get else None

            final_fields = {
                "make": dict_exif.get("Exif.Image.Make", None),
                "model": dict_exif.get("Exif.Image.Model", None),
                "datetime": dict_exif.get("Exif.Image.DateTime", None),
                "gpsStatus": dict_exif.get("Exif.GPSInfo.GPSStatus", None),
                "droneModel": dict_exif.get("Xmp.tiff.Model", None),
                "colorSpace": str(tags.get("EXIF ColorSpace")) if tags.get("EXIF ColorSpace") else None,
                "xmpGpsLatitude": dms_to_decimal(dict_exif.get("Exif.GPSInfo.GPSLatitude", None)),
                "imageWidth":  imageWidth,
                "imageSource": dict_exif.get("Xmp.drone-dji.ImageSource", None),
                "orientation": str(tags.get("Image Orientation")) if tags.get("Image Orientation") else None,
                "altitudeType": dict_exif.get("Xmp.drone-dji.AltitudeType", None),
                "flightXSpeed": dict_exif.get("Xmp.drone-dji.FlightXSpeed", None),
                "flightYSpeed": dict_exif.get("Xmp.drone-dji.FlightYSpeed", None),
                "flightZSpeed": dict_exif.get("Xmp.drone-dji.FlightZSpeed", None),
                "focalLength": focalLength,
                "gpsAltitude": gpsAltitude,
                "gpsLatitude": gpsLatitude,
                "xmpGpsLongitude": dms_to_decimal(dict_exif.get("Exif.GPSInfo.GPSLongitude")) if dict_exif.get("Exif.GPSInfo.GPSLongitude") else None,
                "imageHeight": imageHeight,
                "gpsLongitude": dms_to_decimal(dict_exif.get("Exif.GPSInfo.GPSLongitude")) if dict_exif.get("Exif.GPSInfo.GPSLongitude") else None,
                "gpsMapDatum": dict_exif.get("Exif.GPSInfo.GPSMapDatum", None),
                "gimbalReverse": dict_exif.get("Xmp.drone-dji.GimbalReverse", None),
                "flightYawDegree": dict_exif.get("Xmp.drone-dji.FlightYawDegree", None),
                "focalLength35": focalLength35,
                "gimbalYawDegree": dict_exif.get("Xmp.drone-dji.GimbalYawDegree", None),
                "absoluteAltitude": dict_exif.get("Xmp.drone-dji.AbsoluteAltitude", None),
                "exifImageWidth": exifImageWidth,
                "flightRollDegree": dict_exif.get("Xmp.drone-dji.FlightRollDegree", None),
                "gpsAltitudeRef": dict_exif.get("Exif.GPSInfo.GPSAltitudeRef", None),
                "gimbalRollDegree": dict_exif.get("Xmp.drone-dji.GimbalRollDegree", None),
                "relativeAltitude": dict_exif.get("Xmp.drone-dji.RelativeAltitude", None),
                "droneSerialNumber": dict_exif.get("Exif.Photo.BodySerialNumber", None),
                "exifImageHeight": exifImageHeight,
                "flightPitchDegree": dict_exif.get("Xmp.drone-dji.FlightPitchDegree", None),
                "gimbalPitchDegree": dict_exif.get("Xmp.drone-dji.GimbalPitchDegree", None),
                "imageDescription": dict_exif.get("Exif.Image.ImageDescription", None),
                "cameraSerialNumber": dict_exif.get("Exif.Photo.BodySerialNumber", None),
                "digitalZoomRatio": convert_to_decimal(dict_exif.get("Exif.Photo.DigitalZoomRatio")) if dict_exif.get("Exif.Photo.DigitalZoomRatio") else None
            }

            # 过滤掉所有值为 None 的字段
            final_fields = {k: v for k, v in final_fields.items() if v is not None}

            if final_fields:
                print(f"✓ 成功提取元数据，包含 {len(final_fields)} 个字段")
                print("提取的元数据:")
                for key, value in final_fields.items():
                    print(f"  {key}: {value}")
            else:
                print("⚠ 未提取到任何有效的元数据")

            # 如果没有提取到任何有效的元数据，返回 None
            return final_fields if final_fields else None

    except Exception as e:
        print(f"✗ 自定义元数据提取失败: {e}")
        print(f"错误详情: {traceback.format_exc()}")
        return None

def test_file_format_detection(file_path):
    """测试文件格式检测"""
    print(f"\n=== 文件格式检测 ===")
    
    # 使用magic库检测
    try:
        import magic
        mime_type = magic.from_file(file_path, mime=True)
        print(f"✓ magic检测到MIME类型: {mime_type}")
    except Exception as e:
        print(f"⚠ magic检测失败: {e}")
    
    # 使用mimetypes检测
    try:
        import mimetypes
        mime_type, _ = mimetypes.guess_type(file_path)
        print(f"✓ mimetypes检测到MIME类型: {mime_type}")
    except Exception as e:
        print(f"⚠ mimetypes检测失败: {e}")

async def main():
    """主函数"""
    # 目标文件路径
    target_file = "/Users/<USER>/Codes/inskylab/storage/image/c396daae84a444dafd911fd71a06dd26b54d0eae3bf70da1ec84832e3c59578a.JPG"
    
    print(f"诊断图片元数据提取失败问题")
    print(f"目标文件: {target_file}")
    print("=" * 60)
    
    # 1. 检查依赖库
    if not check_dependencies():
        print("\n❌ 依赖库检查失败，请安装缺失的库")
        return
    
    # 2. 检查文件基本信息
    if not check_file_basic_info(target_file):
        print("\n❌ 文件基本信息检查失败")
        return
    
    # 3. 测试文件格式检测
    test_file_format_detection(target_file)
    
    # 4. 测试PIL
    if not test_pil_image(target_file):
        print("\n❌ PIL测试失败，文件可能损坏或格式不支持")
        return
    
    # 5. 测试pyexiv2
    test_pyexiv2_basic(target_file)
    
    # 6. 测试exifread
    test_exifread_basic(target_file)
    
    # 7. 测试自定义的元数据提取函数
    metadata = await test_custom_metadata_extraction(target_file)
    
    print("\n" + "=" * 60)
    if metadata:
        print("✅ 诊断完成：元数据提取成功")
    else:
        print("❌ 诊断完成：元数据提取失败")
        print("\n可能的原因:")
        print("1. 图片文件不包含EXIF/XMP元数据")
        print("2. 图片格式不被pyexiv2支持")
        print("3. 文件损坏或格式异常")
        print("4. pyexiv2库版本兼容性问题")

if __name__ == "__main__":
    asyncio.run(main())
