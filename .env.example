# AI推理平台环境配置示例
# 复制此文件为 .env 并根据实际环境修改配置值

# ===== 应用基础配置 =====
APP_NAME="AI推理平台"
APP_VERSION="1.0.0"
APP_PORT=8000
DEBUG=false

# ===== API 配置 =====
API_V1_STR="/api/v1"

# ===== 日志配置 =====
LOG_FILE_NAME="app.log"
LOG_FILE_ROTATION="00:00"  # 每日午夜轮转
LOG_FILE_RETENTION_POLICY="7 days"  # 保留7天

# ===== 数据库配置 =====
DATABASE_URL="postgresql://username:password@localhost:5432/ai_platform"
DB_ECHO=false

# ===== Redis 配置 =====
REDIS_URL="redis://localhost:6379/0"

# ===== Celery 配置 =====
CELERY_BROKER_URL="redis://localhost:6379/1"
CELERY_RESULT_BACKEND="redis://localhost:6379/2"

# ===== 存储配置 =====
STORAGE_PATH="/base/path"
HTTP_CLIENT_TIMEOUT=60
FILE_STORAGE_PATH="storage"
FILE_BASE_URL="http://localhost:8000/files"
PLUGIN_STORAGE_PATH="plugins"

# ===== OpenObserve 日志采集配置 =====
OPENOBSERVE_URL="http://**************:5080"
OPENOBSERVE_ORG="default"
OPENOBSERVE_USERNAME="<EMAIL>"
OPENOBSERVE_PASSWORD=""
OPENOBSERVE_STREAM="fastapi-logs"

# ===== 任务调度配置 =====
TASK_SCHEDULER_MAX_CONCURRENT_TASKS=5
TASK_SCHEDULER_GLOBAL_MAX_CONCURRENT_TASKS=30
TASK_SCHEDULER_HEARTBEAT_INTERVAL=10
TASK_SCHEDULER_TASK_TIMEOUT=300
TASK_SCHEDULER_LOCK_TIMEOUT=60
TASK_SCHEDULER_RETRY_MAX_ATTEMPTS=3
TASK_SCHEDULER_RETRY_DELAY_BASE=5
TASK_SCHEDULER_BATCH_SIZE=10
TASK_SCHEDULER_POLL_INTERVAL=1
TASK_SCHEDULER_STALE_TASK_TIMEOUT=300

# ===== 阿里云 OSS 配置 =====
OSS_ACCESS_KEY_ID="your_access_key_id"
OSS_ACCESS_KEY_SECRET="your_access_key_secret"
OSS_ENDPOINT="https://oss-cn-shanghai.aliyuncs.com"
OSS_BUCKET_NAME="your_bucket_name"
OSS_ROLE_ARN="acs:ram::your_account_id:role/your_role_name"
OSS_REGION="cn-shanghai"
