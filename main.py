"""
AI推理平台主应用入口
"""
import uvicorn
from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.exceptions import RequestValidationError
from fastapi.responses import J<PERSON><PERSON>esponse
import os
from contextlib import asynccontextmanager

from app.config.settings import settings
from app.api.v1 import api_router
from app.schemas.base import ErrorResponse
from app.core.log import logger
# 注释掉原有调度器，使用Celery
# from app.core.task.scheduler import scheduler
from app.middleware import PathParamConverterMiddleware, RequestLoggingMiddleware
from app.utils.file import get_storage_file_path
from app.utils.openapi import customize_openapi_schema
from app.core.db import init_sql_logger
# Redis队列已迁移到Celery，不再需要导入
from app.utils.snowflake import init_snowflake_worker


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 应用启动时初始化资源
    logger.info("应用启动，初始化资源...")

    # 初始化SQL日志监听器
    await init_sql_logger()

    # Redis队列已迁移到Celery，不再需要初始化
    logger.info("使用Celery作为任务队列，跳过Redis队列初始化")

    try:
        # 初始化雪花算法工作节点ID
        await init_snowflake_worker()
    except Exception as e:
        logger.warning(f"雪花算法初始化失败，任务调度和ID生成可能受影响: {str(e)}")

    # Celery模式下不需要预初始化全局引擎池
    # 引擎池将在Worker进程中独立管理
    logger.info("使用Celery模式，跳过全局引擎池初始化")

    # Celery模式下不需要启动原有调度器
    # 任务调度由Celery Worker处理
    logger.info("使用Celery模式，跳过原有任务调度器启动")

    # 初始化OpenTelemetry追踪系统
    try:
        from app.core.telemetry import setup_openobserve_integration
        log_handler, trace_enabled = setup_openobserve_integration()

        if trace_enabled:
            # 启用其他OpenTelemetry instrumentation（FastAPI已在应用创建时启用）
            from app.core.telemetry import opentelemetry_trace
            opentelemetry_trace.instrument_requests()
            opentelemetry_trace.instrument_logging()
            opentelemetry_trace.instrument_sqlalchemy()
            opentelemetry_trace.instrument_redis()
            logger.info("OpenTelemetry分布式追踪已启用")
        else:
            logger.info("OpenTelemetry分布式追踪未启用")

    except ImportError:
        logger.warning("OpenTelemetry模块未找到，跳过分布式追踪初始化")
    except Exception as e:
        logger.error(f"OpenTelemetry分布式追踪初始化失败: {str(e)}")

    yield

    # 应用关闭时清理资源
    logger.info("应用关闭，清理资源...")
    
    # Celery模式下不需要关闭原有调度器和全局引擎池
    # Worker进程会独立管理引擎池的生命周期
    logger.info("Celery模式下跳过原有调度器和引擎池关闭")

    # 关闭OpenObserve集成和OpenTelemetry追踪
    try:
        from app.core.telemetry import openobserve_integration
        openobserve_integration.shutdown()
    except ImportError:
        pass
    except Exception as e:
        logger.error(f"OpenObserve集成关闭失败: {str(e)}")

    # 关闭OpenTelemetry追踪
    try:
        from app.core.telemetry import opentelemetry_trace
        if opentelemetry_trace.tracer_provider:
            opentelemetry_trace.tracer_provider.shutdown()
            logger.info("OpenTelemetry追踪已关闭")
    except ImportError:
        pass
    except Exception as e:
        logger.error(f"OpenTelemetry追踪关闭失败: {str(e)}")


app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    description="AI推理平台API",
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url="/docs",
    redoc_url="/redoc",
    debug=settings.DEBUG,
    lifespan=lifespan,
)

# 自定义OpenAPI文档，转换蛇形命名为小驼峰
customize_openapi_schema(app)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加路径参数转换中间件
app.add_middleware(
    PathParamConverterMiddleware,
    exclude_paths=["/docs.*", "/redoc.*", "/openapi.json"],  # 排除文档和OpenAPI路径
)

# 添加请求日志中间件
app.add_middleware(RequestLoggingMiddleware)

# 添加OpenTelemetry中间件栈，用于分布式追踪
# 注意：不能重新赋值app变量，而是通过add_middleware添加
from app.middleware.opentelemetry_middleware import OpenTelemetryTraceMiddleware, OpenTelemetryInstrumentationMiddleware
app.add_middleware(OpenTelemetryInstrumentationMiddleware, service_name=settings.APP_NAME)
app.add_middleware(OpenTelemetryTraceMiddleware, service_name=settings.APP_NAME)

# 初始化OpenTelemetry FastAPI instrumentation（必须在应用创建时完成）
try:
    from app.core.telemetry import opentelemetry_trace
    if opentelemetry_trace.is_enabled:
        opentelemetry_trace.instrument_fastapi(app)
        logger.info("OpenTelemetry FastAPI instrumentation已启用")
except Exception as e:
    logger.error(f"OpenTelemetry FastAPI instrumentation失败: {e}")


# 全局异常处理
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """HTTP异常统一处理"""
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            code=exc.status_code,
            message=str(exc.detail),
            success=False
        ).model_dump(by_alias=True)  # 使用驼峰命名对外
    )


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """请求验证异常统一处理"""
    return JSONResponse(
        status_code=422,
        content=ErrorResponse(
            code=422,
            message=f"请求参数验证失败: {str(exc)}",
            success=False
        ).model_dump(by_alias=True)  # 使用驼峰命名对外
    )


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常统一处理"""
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            code=500,
            message=f"系统内部错误: {str(exc)}",
            success=False
        ).model_dump(by_alias=True)  # 使用驼峰命名对外
    )


# 确保文件存储目录存在
storage_dir = get_storage_file_path(settings.FILE_STORAGE_PATH, absolute=True)
os.makedirs(storage_dir, exist_ok=True)

# 挂载静态文件服务
app.mount("/files", StaticFiles(directory=storage_dir), name="files")

# API路由
app.include_router(api_router, prefix=settings.API_V1_STR)


@app.get("/")
async def root():
    """根路由，返回应用基本信息"""
    return {
        "name": settings.APP_NAME,
        "version": settings.APP_VERSION,
        "status": "running",
    }


if __name__ == "__main__":
    """直接运行时启动应用"""
    # 设置日志级别
    log_level = "debug" if settings.DEBUG else "info"
    
    # 计算工作进程数（生产环境使用）
    # 如果是开发环境(DEBUG=True)，使用单进程以支持热重载
    # OpenTelemetry多进程支持已优化，可以安全使用多进程
    workers = 1 if settings.DEBUG else 2
    
    # 启动 uvicorn
    uvicorn.run(
        "main:app", 
        host="0.0.0.0", 
        port=settings.APP_PORT,
        reload=settings.DEBUG,
        log_level=log_level,
        workers=workers,  # 添加workers参数
        access_log=False  # 禁用uvicorn访问日志
    )
